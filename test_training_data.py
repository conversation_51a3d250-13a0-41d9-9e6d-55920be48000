#!/usr/bin/env python3
"""
Test script to demonstrate the difference between full dataset and limited dataset training
"""

import pandas as pd
from pathlib import Path
import sys

def load_data():
    """Load the 1h data file"""
    data_file = Path("data/1h_3112020_3062025.csv")
    
    if not data_file.exists():
        print(f"❌ Data file not found: {data_file}")
        return None
    
    print(f"📊 Loading data from {data_file}")
    df = pd.read_csv(data_file)
    
    # Convert time column to datetime
    df['time'] = pd.to_datetime(df['time'])
    df.set_index('time', inplace=True)
    
    # Sort by time
    df.sort_index(inplace=True)
    
    return df

def analyze_data_usage():
    """Analyze different data usage scenarios"""
    df = load_data()
    if df is None:
        return
    
    print("🚀 BTC/USDT Training Data Analysis")
    print("=" * 50)
    
    total_records = len(df)
    date_start = df.index.min()
    date_end = df.index.max()
    price_start = df['close'].iloc[0]
    price_end = df['close'].iloc[-1]
    price_min = df['close'].min()
    price_max = df['close'].max()
    
    print(f"📈 FULL DATASET OVERVIEW:")
    print(f"   Total Records: {total_records:,}")
    print(f"   Date Range: {date_start} to {date_end}")
    print(f"   Duration: {(date_end - date_start).days} days ({(date_end - date_start).days/365.25:.1f} years)")
    print(f"   Price Range: ${price_min:,.2f} to ${price_max:,.2f}")
    print(f"   Price Evolution: ${price_start:,.2f} → ${price_end:,.2f} ({((price_end/price_start-1)*100):+.1f}%)")
    print()
    
    # Analyze different training scenarios
    scenarios = [
        ("FULL DATASET (NEW)", total_records, "All available data"),
        ("RECENT SUBSET (OLD)", 20000, "Last 20,000 records only"),
        ("MEDIUM SUBSET", 30000, "Last 30,000 records"),
        ("LARGE SUBSET", 40000, "Last 40,000 records")
    ]
    
    print("📊 TRAINING SCENARIOS COMPARISON:")
    print("-" * 50)
    
    for name, records, description in scenarios:
        if records > total_records:
            continue
            
        subset = df.tail(records)
        subset_start = subset.index.min()
        subset_end = subset.index.max()
        subset_duration = (subset_end - subset_start).days
        subset_price_start = subset['close'].iloc[0]
        subset_price_end = subset['close'].iloc[-1]
        
        print(f"🎯 {name}:")
        print(f"   Description: {description}")
        print(f"   Records: {records:,} ({records/total_records*100:.1f}% of total)")
        print(f"   Date Range: {subset_start.strftime('%Y-%m-%d')} to {subset_end.strftime('%Y-%m-%d')}")
        print(f"   Duration: {subset_duration} days ({subset_duration/365.25:.1f} years)")
        print(f"   Price Range: ${subset_price_start:,.2f} → ${subset_price_end:,.2f}")
        print(f"   Training Time Est: {estimate_training_time(records)} minutes")
        print()
    
    # Market cycle analysis
    print("📈 MARKET CYCLE COVERAGE ANALYSIS:")
    print("-" * 50)
    
    # Define major market events (approximate dates)
    events = [
        ("COVID Crash", "2020-03-01", "2020-04-01"),
        ("Bull Run 2020-2021", "2020-10-01", "2021-11-01"),
        ("Bear Market 2022", "2021-11-01", "2022-12-01"),
        ("Recovery 2023", "2023-01-01", "2023-12-01"),
        ("Bull Market 2024-2025", "2024-01-01", "2025-06-01")
    ]
    
    # Check coverage for different scenarios
    recent_20k = df.tail(20000)
    recent_start = recent_20k.index.min()
    
    print(f"FULL DATASET covers:")
    for event, start_date, end_date in events:
        start_dt = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)
        if start_dt >= date_start and end_dt <= date_end:
            print(f"   ✅ {event}")
        else:
            print(f"   ❌ {event} (partial/missing)")
    
    print(f"\nRECENT 20K RECORDS (from {recent_start.strftime('%Y-%m-%d')}) covers:")
    for event, start_date, end_date in events:
        start_dt = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)
        if start_dt >= recent_start:
            print(f"   ✅ {event}")
        else:
            print(f"   ❌ {event} (before training data)")
    
    print()
    print("🎯 RECOMMENDATION:")
    print("   ✅ Use FULL DATASET for comprehensive market cycle learning")
    print("   ✅ Better generalization across all market conditions")
    print("   ✅ More robust predictions in various market regimes")
    print("   ⚠️  Longer training time but significantly better model quality")

def estimate_training_time(records):
    """Estimate training time based on number of records"""
    # Rough estimates based on typical performance
    base_time = 30  # Base time for 20k records
    scale_factor = records / 20000
    return int(base_time * scale_factor)

def show_current_config():
    """Show current training configuration"""
    try:
        # Read config file
        config_file = Path("config.py")
        if not config_file.exists():
            print("❌ Config file not found")
            return
        
        with open(config_file, 'r') as f:
            content = f.read()
        
        print("⚙️  CURRENT CONFIGURATION:")
        print("-" * 30)
        
        if '"use_full_dataset": True' in content:
            if '"max_training_records": None' in content:
                print("✅ Mode: FULL DATASET")
                print("📊 Will use ALL available records")
            else:
                # Extract max_training_records value
                import re
                match = re.search(r'"max_training_records": (\d+)', content)
                if match:
                    max_records = int(match.group(1))
                    print("✅ Mode: LIMITED DATASET")
                    print(f"📊 Will use last {max_records:,} records")
        else:
            print("✅ Mode: RECENT SUBSET")
            print("📊 Will use last 20,000 records (legacy)")
        
    except Exception as e:
        print(f"❌ Error reading config: {e}")

def main():
    """Main function"""
    if len(sys.argv) > 1 and sys.argv[1] == "--config":
        show_current_config()
    else:
        analyze_data_usage()
        print()
        show_current_config()
        print()
        print("💡 To change configuration:")
        print("   python3 configure_training.py --full    # Use full dataset")
        print("   python3 configure_training.py --recent  # Use recent subset")

if __name__ == "__main__":
    main()

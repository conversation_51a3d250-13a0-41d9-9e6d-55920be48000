# Trained Models Directory

This directory contains the trained machine learning models for the BTC/USDT price prediction system.

## ⚠️ Important Note

**The trained model files are not included in the Git repository** due to their large size (>100MB each). You need to train the models locally after cloning the repository.

## 📁 Directory Structure

```
models/
├── NeuralNetwork/
│   ├── NeuralNetwork_4h.h5      # 4-hour prediction model
│   ├── NeuralNetwork_16h.h5     # 16-hour prediction model
│   ├── NeuralNetwork_24h.h5     # 24-hour prediction model
│   └── scaler.joblib            # Feature scaler
├── RandomForest/
│   ├── RandomForest_4h.joblib   # 4-hour prediction model (~121MB)
│   ├── RandomForest_16h.joblib  # 16-hour prediction model (~121MB)
│   ├── RandomForest_24h.joblib  # 24-hour prediction model (~121MB)
│   └── scaler.joblib            # Feature scaler
├── GradientBoosting/
│   ├── GradientBoosting_4h.joblib   # 4-hour prediction model
│   ├── GradientBoosting_16h.joblib  # 16-hour prediction model
│   ├── GradientBoosting_24h.joblib  # 24-hour prediction model
│   └── scaler.joblib                # Feature scaler
└── README.md                    # This file
```

## 🚀 How to Generate Models

After cloning the repository, follow these steps to train the models:

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Verify Data Files

Ensure you have the required data files in the `data/` directory:

- `15m_3112020_3062025.csv`
- `1h_3112020_3062025.csv`
- `4h_3112020_3062025.csv`
- `1d_3112020_3062025.csv`
- `1w_3112020_3062025.csv`
- `1M_3112020_3062025.csv`

### 3. Test Data Processing

```bash
python3 test_preprocessing.py
```

### 4. Train All Models

```bash
python3 train_models.py
```

This will:

- Load and preprocess the historical data
- Train Neural Network, Random Forest, and Gradient Boosting models
- Save the trained models to this directory
- Generate performance metrics and evaluation reports

### 5. Verify Model Training

After training completes, you should see:

- Model files in their respective subdirectories
- Training logs with performance metrics
- `evaluation_results.json` with model comparison

## ⏱️ Training Time Estimates (Full Dataset - 47K records)

- **Neural Network**: ~15-25 minutes
- **Random Forest**: ~30-45 minutes (largest models)
- **Gradient Boosting**: ~20-30 minutes

Total training time: ~60-90 minutes (depending on your hardware)

## 💾 Model File Sizes

- **Neural Network models**: ~10-50MB each
- **Random Forest models**: ~121MB each (largest)
- **Gradient Boosting models**: ~20-80MB each
- **Scalers**: ~1-5MB each

## 🔄 Retraining Models

To retrain models with new data:

1. Update data files in the `data/` directory
2. Run: `python3 train_models.py`
3. Restart the API: `python3 run_api.py`

## 🚨 Troubleshooting

### Models Not Loading

- Ensure all model files exist in their respective directories
- Check file permissions (should be readable)
- Verify models were trained successfully (check logs)

### Training Fails

- Check data files are present and properly formatted
- Ensure sufficient disk space (>2GB recommended)
- Verify Python dependencies are installed

### Out of Memory Errors

- Reduce the dataset size in `config.py`
- Close other applications to free up RAM
- Consider using a machine with more memory

## 📊 Model Performance

Latest training results (example):

| Model             | 4h RMSE  | 16h RMSE | 24h RMSE | 4h R²  | 16h R² | 24h R² |
| ----------------- | -------- | -------- | -------- | ------ | ------ | ------ |
| Neural Network    | 1,247.32 | 2,891.45 | 3,456.78 | 0.9876 | 0.9654 | 0.9432 |
| Random Forest     | 2,345.67 | 3,456.78 | 4,567.89 | 0.9234 | 0.8765 | 0.8234 |
| Gradient Boosting | 2,123.45 | 3,234.56 | 4,345.67 | 0.9345 | 0.8876 | 0.8345 |

## 🔗 Related Files

- `train_models.py` - Main training script
- `config.py` - Training configuration
- `src/models/` - Model implementations
- `src/data_processing/` - Data preprocessing code

## 📝 Notes

- Models are automatically saved after successful training
- Each model includes its own feature scaler
- Training progress is logged to console and log files
- Models are optimized for the specific prediction horizons (4h, 16h, 24h)

For more information, see the main [README.md](../README.md) file.

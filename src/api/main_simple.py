"""
Simplified FastAPI application for BTC/USDT price prediction
This version only uses Neural Network and Random Forest models
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
import json

from fastapi import FastAP<PERSON>, HTTPException, Request
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import our modules
from src.data_processing.data_loader import DataLoader
from src.data_processing.preprocessor import DataPreprocessor, PreprocessingConfig

# Create FastAPI app
app = FastAPI(
    title="BTC/USDT Price Prediction API",
    description="Machine Learning API for Bitcoin price prediction",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Global variables
latest_data = None
original_data = None
last_update = None
preprocessor = None
cached_features = None
cached_current_price = None
loaded_models = {}

# Timezone for display
UTC_PLUS_7 = timezone(timedelta(hours=7))

def get_current_time_utc7():
    """Get current time in UTC+7"""
    return datetime.now(UTC_PLUS_7)

def load_models():
    """Load available trained models"""
    global loaded_models
    
    models_dir = Path("models")
    available_models = ["NeuralNetwork", "RandomForest"]  # Only these two
    
    for model_name in available_models:
        model_dir = models_dir / model_name
        if model_dir.exists():
            try:
                # Check if model files exist
                model_files = list(model_dir.glob("*.joblib"))
                if model_files:
                    loaded_models[model_name] = {
                        "path": model_dir,
                        "loaded": False,
                        "model": None
                    }
                    logger.info(f"Found {model_name} model files")
                else:
                    logger.warning(f"No model files found for {model_name}")
            except Exception as e:
                logger.error(f"Error checking {model_name} model: {e}")
    
    logger.info(f"Available models: {list(loaded_models.keys())}")

@app.on_event("startup")
async def startup_event():
    """Initialize the API on startup"""
    global preprocessor
    
    try:
        logger.info("Starting BTC/USDT Price Prediction API...")
        
        # Load models
        load_models()
        
        # Initialize preprocessor
        config = PreprocessingConfig(
            sequence_length=60,
            prediction_horizons=[4, 16, 24],
            features=['open', 'high', 'low', 'close', 'volume'],
            target='close',
            scaler_type='standard'
        )
        preprocessor = DataPreprocessor(config)
        
        # Load and preprocess latest data
        await update_data()
        
        logger.info("API startup completed successfully!")
        
    except Exception as e:
        logger.error(f"Failed to initialize API: {e}")
        raise

async def update_data():
    """Update latest data for predictions"""
    global latest_data, original_data, last_update, preprocessor, cached_features, cached_current_price

    try:
        logger.info("Updating latest data...")

        # Load recent data
        loader = DataLoader()
        df_1h = loader.load_timeframe_data('1h')

        # Use last 5000 records for preprocessing context
        recent_data = df_1h.tail(5000).copy()

        # Store original data (before preprocessing)
        original_data = recent_data.copy()

        # Preprocess data
        if preprocessor.is_fitted:
            processed_data = preprocessor.transform(recent_data)
        else:
            processed_data = preprocessor.fit_transform(recent_data)

        latest_data = processed_data
        last_update = get_current_time_utc7()

        # Cache latest features and current price for faster predictions
        cached_features = latest_data.tail(1)
        cached_current_price = float(original_data['close'].iloc[-1])

        logger.info(f"Data updated successfully. Latest data shape: {latest_data.shape}")

    except Exception as e:
        logger.error(f"Failed to update data: {e}")
        raise

@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve the main dashboard"""
    return FileResponse("templates/index.html")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": get_current_time_utc7().isoformat(),
        "models_loaded": len(loaded_models),
        "available_models": list(loaded_models.keys()),
        "data_last_update": last_update.isoformat() if last_update else None,
        "data_records": len(latest_data) if latest_data is not None else 0
    }

@app.get("/current-price")
async def get_current_price():
    """Get the current BTC/USDT price"""
    if original_data is None:
        raise HTTPException(status_code=503, detail="Data not available")
    
    try:
        current_price = float(original_data['close'].iloc[-1])
        timestamp = original_data.index[-1]
        
        # Convert to UTC+7 for display
        if timestamp.tz is None:
            timestamp = timestamp.tz_localize('UTC').tz_convert(UTC_PLUS_7)
        else:
            timestamp = timestamp.tz_convert(UTC_PLUS_7)
        
        return {
            "price": current_price,
            "timestamp": timestamp.isoformat(),
            "currency": "USDT",
            "symbol": "BTC/USDT"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get current price: {str(e)}")

@app.get("/models")
async def get_models():
    """Get information about available models"""
    models_info = {}
    
    for model_name, model_info in loaded_models.items():
        try:
            model_dir = model_info["path"]
            
            # Read metadata if available
            metadata_file = model_dir / f"{model_name}_metadata.json"
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
            else:
                metadata = {}
            
            models_info[model_name] = {
                "name": model_name,
                "available": True,
                "metadata": metadata,
                "model_files": len(list(model_dir.glob("*.joblib")))
            }
        except Exception as e:
            models_info[model_name] = {
                "name": model_name,
                "available": False,
                "error": str(e)
            }
    
    return {
        "models": models_info,
        "total_models": len(models_info)
    }

@app.get("/historical-data")
async def get_historical_data(hours: int = 168):  # Default 1 week
    """Get historical price data"""
    if original_data is None:
        raise HTTPException(status_code=503, detail="Data not available")

    try:
        # Get last N hours of data from original (unscaled) data
        historical = original_data.tail(hours)

        data = []
        for idx, row in historical.iterrows():
            data.append({
                "timestamp": idx.isoformat(),
                "open": float(row['open']),
                "high": float(row['high']),
                "low": float(row['low']),
                "close": float(row['close']),
                "volume": float(row['volume'])
            })

        return {
            "data": data,
            "count": len(data),
            "timeframe": "1h"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get historical data: {str(e)}")

@app.get("/training-info")
async def get_training_info():
    """Get detailed training data information"""
    try:
        from src.data_processing.data_loader import DataLoader

        loader = DataLoader()
        df = loader.load_timeframe_data('1h')

        # Get training data subset (full dataset as configured)
        from config import MODEL_CONFIG
        if MODEL_CONFIG.get("use_full_dataset", True) and MODEL_CONFIG.get("max_training_records") is None:
            training_data = df  # Use full dataset
        else:
            # Use recent records (legacy behavior or limited dataset)
            max_records = MODEL_CONFIG.get("max_training_records", 20000)
            training_data = df.tail(max_records)

        total_records = len(df)
        training_records = len(training_data)
        
        # Calculate date ranges
        full_start = df.index.min()
        full_end = df.index.max()
        training_start = training_data.index.min()
        training_end = training_data.index.max()
        
        # Calculate price information
        start_price = float(df['close'].iloc[0])
        end_price = float(df['close'].iloc[-1])
        min_price = float(df['close'].min())
        max_price = float(df['close'].max())
        
        return {
            "dataset_overview": {
                "total_records": total_records,
                "full_date_range": {
                    "start": full_start.isoformat(),
                    "end": full_end.isoformat()
                },
                "price_evolution": {
                    "start_price": start_price,
                    "end_price": end_price,
                    "min_price": min_price,
                    "max_price": max_price,
                    "total_change_percent": ((end_price / start_price - 1) * 100)
                }
            },
            "training_data": {
                "records_used": training_records,
                "percentage_of_total": (training_records / total_records * 100),
                "date_range": {
                    "start": training_start.isoformat(),
                    "end": training_end.isoformat()
                },
                "duration_days": (training_end - training_start).days,
                "duration_years": (training_end - training_start).days / 365.25
            },
            "available_models": list(loaded_models.keys()),
            "last_update": last_update.isoformat() if last_update else None
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get training info: {str(e)}")

# Simple prediction endpoint (mock for now since we need to load actual models)
@app.post("/predict")
async def predict_price(request: Dict[str, Any]):
    """Make price predictions (simplified version)"""
    if latest_data is None:
        raise HTTPException(status_code=503, detail="Data not available")
    
    model_name = request.get("model_name", "NeuralNetwork")
    include_confidence = request.get("include_confidence", True)
    
    if model_name not in loaded_models:
        raise HTTPException(status_code=400, detail=f"Model {model_name} not available")
    
    try:
        current_price = cached_current_price if cached_current_price is not None else float(original_data['close'].iloc[-1])

        # Use the timestamp of the latest data point
        latest_data_timestamp = original_data.index[-1]
        if latest_data_timestamp.tz is None:
            base_time = latest_data_timestamp.tz_localize('UTC').tz_convert(UTC_PLUS_7)
        else:
            base_time = latest_data_timestamp.tz_convert(UTC_PLUS_7)

        # Mock predictions for now (replace with actual model loading and prediction)
        predictions = {}
        for horizon in [4, 16, 24]:
            # Simple mock prediction (current price + small random variation)
            base_change = np.random.normal(0, 0.02)  # 2% standard deviation
            predicted_price = current_price * (1 + base_change * (horizon / 24))

            # Calculate prediction timestamp
            prediction_time = base_time + timedelta(hours=horizon)

            prediction = {
                "price": round(predicted_price, 2),
                "horizon_hours": horizon,
                "change_percent": round(((predicted_price / current_price - 1) * 100), 2),
                "prediction_time": prediction_time.isoformat()
            }

            if include_confidence:
                prediction["confidence_interval"] = {
                    "lower": round(predicted_price * 0.95, 2),
                    "upper": round(predicted_price * 1.05, 2),
                    "confidence_level": 0.95
                }

            predictions[f"{horizon}h"] = prediction

        return {
            "model_name": model_name,
            "current_price": current_price,
            "latest_data_time": base_time.isoformat(),
            "predictions": predictions,
            "note": f"Predictions from latest data: {base_time.strftime('%Y-%m-%d %H:%M:%S %Z')}"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")

@app.get("/hourly-predictions/{hours}")
async def get_hourly_predictions(hours: int, model: str = "NeuralNetwork"):
    """Get hourly predictions for a specific time horizon"""
    if latest_data is None:
        raise HTTPException(status_code=503, detail="Data not available")

    if model not in loaded_models:
        raise HTTPException(status_code=400, detail=f"Model {model} not available")

    try:
        current_price = cached_current_price if cached_current_price is not None else float(original_data['close'].iloc[-1])

        # Use the timestamp of the latest data point, not current system time
        latest_data_timestamp = original_data.index[-1]

        # Convert to UTC+7 for display if needed
        if latest_data_timestamp.tz is None:
            base_time = latest_data_timestamp.tz_localize('UTC').tz_convert(UTC_PLUS_7)
        else:
            base_time = latest_data_timestamp.tz_convert(UTC_PLUS_7)

        # Generate hourly predictions for the requested time horizon
        predictions = []

        for hour in range(1, hours + 1):
            # Simple mock prediction logic (replace with actual model when available)
            base_change = np.random.normal(0, 0.01)  # 1% standard deviation per hour
            time_factor = hour / 24.0  # Scale by time
            predicted_price = current_price * (1 + base_change * time_factor)

            # Prediction time starts from the latest data timestamp
            prediction_time = base_time + timedelta(hours=hour)

            predictions.append({
                "hour": hour,
                "timestamp": prediction_time.isoformat(),
                "predicted_price": round(predicted_price, 2),
                "change_from_current": round(((predicted_price / current_price - 1) * 100), 2),
                "confidence": round(max(0.5, 1.0 - (hour * 0.02)), 2)  # Decreasing confidence over time
            })

        return {
            "model_name": model,
            "current_price": current_price,
            "latest_data_time": base_time.isoformat(),
            "predictions": predictions,
            "total_hours": hours,
            "note": f"Predictions start from latest data point: {base_time.strftime('%Y-%m-%d %H:%M:%S %Z')}"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get hourly predictions: {str(e)}")

@app.post("/retrain")
async def retrain_models(request: Dict[str, Any]):
    """Mock retrain endpoint (not implemented in simple API)"""
    return {
        "message": "Retraining not available in simple API mode",
        "note": "Use the full API (./run.sh --full) for model retraining functionality",
        "requested_models": request.get('models', []),
        "status": "not_implemented"
    }

@app.get("/model-performance/{model_name}")
async def get_model_performance(model_name: str):
    """Get mock model performance metrics"""
    if model_name not in loaded_models:
        raise HTTPException(status_code=404, detail=f"Model {model_name} not found")

    # Return mock performance data
    return {
        "model_name": model_name,
        "performance_metrics": {
            "rmse_4h": round(np.random.uniform(1000, 2000), 2),
            "rmse_16h": round(np.random.uniform(2000, 3000), 2),
            "rmse_24h": round(np.random.uniform(3000, 4000), 2),
            "r2_4h": round(np.random.uniform(0.85, 0.95), 3),
            "r2_16h": round(np.random.uniform(0.75, 0.85), 3),
            "r2_24h": round(np.random.uniform(0.65, 0.75), 3),
            "mae_4h": round(np.random.uniform(800, 1500), 2),
            "mae_16h": round(np.random.uniform(1500, 2500), 2),
            "mae_24h": round(np.random.uniform(2500, 3500), 2)
        },
        "training_info": {
            "training_date": "2025-06-26",
            "training_records": 47271,
            "training_duration": "60-90 minutes",
            "model_size": "121MB" if model_name == "RandomForest" else "10MB"
        },
        "note": "Mock performance data - use full API for actual metrics"
    }

@app.post("/update-data")
async def trigger_data_update():
    """Trigger data update"""
    try:
        await update_data()
        return {
            "message": "Data updated successfully",
            "timestamp": get_current_time_utc7().isoformat(),
            "records": len(latest_data) if latest_data is not None else 0
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update data: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)

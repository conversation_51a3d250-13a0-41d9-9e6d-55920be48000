"""
Random Forest model for cryptocurrency price prediction
"""
import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import GridSearchCV
from typing import Dict, List, Tuple, Any
import logging

from .base_model import BaseModel

logger = logging.getLogger(__name__)

class RandomForestModel(BaseModel):
    """Random Forest model for price prediction"""
    
    def __init__(self, prediction_horizons: List[int] = [4, 16, 24], 
                 n_estimators: int = 100, max_depth: int = None,
                 min_samples_split: int = 2, min_samples_leaf: int = 1,
                 random_state: int = 42, n_jobs: int = -1):
        super().__init__("RandomForest", prediction_horizons)
        
        self.n_estimators = n_estimators
        self.max_depth = max_depth
        self.min_samples_split = min_samples_split
        self.min_samples_leaf = min_samples_leaf
        self.random_state = random_state
        self.n_jobs = n_jobs
        
    def _build_model(self, input_shape: Tuple, horizon: int) -> RandomForestRegressor:
        """Build Random Forest model"""
        model = RandomForestRegressor(
            n_estimators=self.n_estimators,
            max_depth=self.max_depth,
            min_samples_split=self.min_samples_split,
            min_samples_leaf=self.min_samples_leaf,
            random_state=self.random_state,
            n_jobs=self.n_jobs,
            verbose=0
        )
        
        return model
    
    def _train_model(self, model: RandomForestRegressor, X_train: np.ndarray, y_train: np.ndarray,
                    X_val: np.ndarray, y_val: np.ndarray, horizon: int) -> RandomForestRegressor:
        """Train Random Forest model"""
        logger.info(f"Training Random Forest for {horizon}h horizon...")
        
        # Fit the model
        model.fit(X_train, y_train)
        
        return model
    
    def _predict(self, model: RandomForestRegressor, X: np.ndarray) -> np.ndarray:
        """Make predictions"""
        return model.predict(X)
    
    def get_feature_importance(self) -> Dict[str, np.ndarray]:
        """Get feature importance for each horizon"""
        importance = {}
        
        for horizon in self.prediction_horizons:
            if f'{horizon}h' in self.models:
                model = self.models[f'{horizon}h']
                importance[f'{horizon}h'] = model.feature_importances_
        
        return importance
    
    def get_feature_importance_df(self) -> Dict[str, pd.DataFrame]:
        """Get feature importance as DataFrame"""
        importance_dfs = {}
        importance = self.get_feature_importance()
        
        for horizon_key, importances in importance.items():
            df = pd.DataFrame({
                'feature': self.feature_columns,
                'importance': importances
            }).sort_values('importance', ascending=False)
            
            importance_dfs[horizon_key] = df
        
        return importance_dfs


class OptimizedRandomForestModel(RandomForestModel):
    """Random Forest model with hyperparameter optimization"""
    
    def __init__(self, prediction_horizons: List[int] = [4, 16, 24],
                 optimize_hyperparams: bool = True, cv_folds: int = 3,
                 random_state: int = 42, n_jobs: int = -1):
        super().__init__(prediction_horizons=prediction_horizons, 
                        random_state=random_state, n_jobs=n_jobs)
        
        self.optimize_hyperparams = optimize_hyperparams
        self.cv_folds = cv_folds
        self.best_params = {}
        
        # Parameter grid for optimization
        self.param_grid = {
            'n_estimators': [50, 100, 200],
            'max_depth': [10, 20, 30, None],
            'min_samples_split': [2, 5, 10],
            'min_samples_leaf': [1, 2, 4],
            'max_features': ['sqrt', 'log2', None]
        }
    
    def _build_model(self, input_shape: Tuple, horizon: int) -> RandomForestRegressor:
        """Build optimized Random Forest model"""
        if self.optimize_hyperparams:
            # Use grid search for hyperparameter optimization
            base_model = RandomForestRegressor(
                random_state=self.random_state,
                n_jobs=self.n_jobs
            )
            
            model = GridSearchCV(
                base_model,
                self.param_grid,
                cv=self.cv_folds,
                scoring='neg_mean_squared_error',
                n_jobs=self.n_jobs,
                verbose=1
            )
        else:
            model = super()._build_model(input_shape, horizon)
        
        return model
    
    def _train_model(self, model, X_train: np.ndarray, y_train: np.ndarray,
                    X_val: np.ndarray, y_val: np.ndarray, horizon: int):
        """Train optimized Random Forest model"""
        logger.info(f"Training optimized Random Forest for {horizon}h horizon...")
        
        if self.optimize_hyperparams:
            # Fit with grid search
            model.fit(X_train, y_train)
            
            # Store best parameters
            self.best_params[f'{horizon}h'] = model.best_params_
            logger.info(f"Best parameters for {horizon}h: {model.best_params_}")
            
            # Return the best estimator
            return model.best_estimator_
        else:
            return super()._train_model(model, X_train, y_train, X_val, y_val, horizon)
    
    def _predict(self, model, X: np.ndarray) -> np.ndarray:
        """Make predictions with optimized model"""
        return model.predict(X)


class EnsembleRandomForestModel(BaseModel):
    """Ensemble of Random Forest models with different configurations"""
    
    def __init__(self, prediction_horizons: List[int] = [4, 16, 24],
                 n_models: int = 3, random_state: int = 42):
        super().__init__("EnsembleRandomForest", prediction_horizons)
        
        self.n_models = n_models
        self.random_state = random_state
        self.ensemble_models = {}
        
        # Different configurations for ensemble
        self.model_configs = [
            {'n_estimators': 100, 'max_depth': 20, 'min_samples_split': 2},
            {'n_estimators': 200, 'max_depth': 30, 'min_samples_split': 5},
            {'n_estimators': 150, 'max_depth': None, 'min_samples_split': 10}
        ]
    
    def _build_model(self, input_shape: Tuple, horizon: int) -> List[RandomForestRegressor]:
        """Build ensemble of Random Forest models"""
        models = []
        
        for i in range(self.n_models):
            config = self.model_configs[i % len(self.model_configs)]
            
            model = RandomForestRegressor(
                random_state=self.random_state + i,
                n_jobs=-1,
                **config
            )
            
            models.append(model)
        
        return models
    
    def _train_model(self, models: List[RandomForestRegressor], X_train: np.ndarray, y_train: np.ndarray,
                    X_val: np.ndarray, y_val: np.ndarray, horizon: int) -> List[RandomForestRegressor]:
        """Train ensemble of Random Forest models"""
        logger.info(f"Training ensemble of {len(models)} Random Forest models for {horizon}h horizon...")
        
        trained_models = []
        for i, model in enumerate(models):
            logger.info(f"Training model {i+1}/{len(models)}...")
            model.fit(X_train, y_train)
            trained_models.append(model)
        
        return trained_models
    
    def _predict(self, models: List[RandomForestRegressor], X: np.ndarray) -> np.ndarray:
        """Make ensemble predictions"""
        predictions = []
        
        for model in models:
            pred = model.predict(X)
            predictions.append(pred)
        
        # Average predictions
        ensemble_pred = np.mean(predictions, axis=0)
        
        return ensemble_pred
    
    def get_feature_importance(self) -> Dict[str, np.ndarray]:
        """Get averaged feature importance across ensemble"""
        importance = {}
        
        for horizon in self.prediction_horizons:
            if f'{horizon}h' in self.models:
                models = self.models[f'{horizon}h']
                
                # Average feature importance across models
                importances = []
                for model in models:
                    importances.append(model.feature_importances_)
                
                avg_importance = np.mean(importances, axis=0)
                importance[f'{horizon}h'] = avg_importance
        
        return importance

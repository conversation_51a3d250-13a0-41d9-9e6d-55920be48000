"""
Model training and evaluation utilities
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import logging
from pathlib import Path
import json
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

from .base_model import BaseModel
from .random_forest_model import RandomForestModel, OptimizedRandomForestModel, EnsembleRandomForestModel
from .neural_network_model import NeuralNetworkModel, OptimizedNeuralNetworkModel, DeepNeuralNetworkModel
from .gradient_boosting_model import GradientBoostingModel, OptimizedGradientBoostingModel

logger = logging.getLogger(__name__)

class ModelTrainer:
    """Train and evaluate multiple models for price prediction"""
    
    def __init__(self, models_dir: Path = Path("models")):
        self.models_dir = Path(models_dir)
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        self.trained_models = {}
        self.evaluation_results = {}
        
    def get_available_models(self) -> Dict[str, BaseModel]:
        """Get dictionary of available model classes"""
        return {
            'RandomForest': RandomForestModel(),
            'OptimizedRandomForest': OptimizedRandomForestModel(),
            'EnsembleRandomForest': EnsembleRandomForestModel(),
            'NeuralNetwork': NeuralNetworkModel(),
            'OptimizedNeuralNetwork': OptimizedNeuralNetworkModel(),
            'DeepNeuralNetwork': DeepNeuralNetworkModel(),
            'GradientBoosting': GradientBoostingModel(),
            'OptimizedGradientBoosting': OptimizedGradientBoostingModel()
        }
    
    def train_model(self, model_name: str, data: pd.DataFrame, 
                   train_ratio: float = 0.7, val_ratio: float = 0.15) -> BaseModel:
        """Train a specific model"""
        available_models = self.get_available_models()
        
        if model_name not in available_models:
            raise ValueError(f"Model {model_name} not available. Available: {list(available_models.keys())}")
        
        logger.info(f"Training {model_name} model...")
        
        # Get model instance
        model = available_models[model_name]
        
        # Train the model
        model.train(data, train_ratio, val_ratio)
        
        # Save the model
        model_dir = self.models_dir / model_name
        model.save_model(model_dir)
        
        # Store in trained models
        self.trained_models[model_name] = model
        
        logger.info(f"{model_name} training completed and saved!")
        
        return model
    
    def train_all_models(self, data: pd.DataFrame, 
                        model_names: Optional[List[str]] = None,
                        train_ratio: float = 0.7, val_ratio: float = 0.15) -> Dict[str, BaseModel]:
        """Train multiple models"""
        available_models = self.get_available_models()
        
        if model_names is None:
            # Train fast models by default (exclude optimized ones for speed)
            model_names = ['RandomForest', 'NeuralNetwork', 'GradientBoosting']
        
        trained_models = {}
        
        for model_name in model_names:
            try:
                model = self.train_model(model_name, data, train_ratio, val_ratio)
                trained_models[model_name] = model
            except Exception as e:
                logger.error(f"Failed to train {model_name}: {e}")
        
        return trained_models
    
    def evaluate_model(self, model_name: str, data: pd.DataFrame) -> Dict[str, Dict[str, float]]:
        """Evaluate a trained model"""
        if model_name not in self.trained_models:
            # Try to load the model
            model_dir = self.models_dir / model_name
            if model_dir.exists():
                available_models = self.get_available_models()
                model = available_models[model_name]
                model.load_model(model_dir)
                self.trained_models[model_name] = model
            else:
                raise ValueError(f"Model {model_name} not found. Train it first.")
        
        model = self.trained_models[model_name]
        results = model.evaluate(data)
        
        self.evaluation_results[model_name] = results
        
        return results
    
    def evaluate_all_models(self, data: pd.DataFrame) -> Dict[str, Dict[str, Dict[str, float]]]:
        """Evaluate all trained models"""
        results = {}
        
        for model_name in self.trained_models.keys():
            try:
                results[model_name] = self.evaluate_model(model_name, data)
            except Exception as e:
                logger.error(f"Failed to evaluate {model_name}: {e}")
        
        return results
    
    def compare_models(self, data: pd.DataFrame, metric: str = 'rmse') -> pd.DataFrame:
        """Compare models performance"""
        if not self.evaluation_results:
            self.evaluate_all_models(data)
        
        comparison_data = []
        
        for model_name, results in self.evaluation_results.items():
            for horizon, metrics in results.items():
                comparison_data.append({
                    'Model': model_name,
                    'Horizon': horizon,
                    'RMSE': metrics['rmse'],
                    'MAE': metrics['mae'],
                    'MAPE': metrics['mape'],
                    'R2': metrics['r2'],
                    'Directional_Accuracy': metrics['directional_accuracy']
                })
        
        comparison_df = pd.DataFrame(comparison_data)
        
        return comparison_df
    
    def plot_model_comparison(self, data: pd.DataFrame, save_path: Optional[Path] = None):
        """Plot model comparison"""
        comparison_df = self.compare_models(data)
        
        # Create subplots for different metrics
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Model Performance Comparison', fontsize=16)
        
        metrics = ['RMSE', 'MAE', 'MAPE', 'Directional_Accuracy']
        
        for i, metric in enumerate(metrics):
            ax = axes[i // 2, i % 2]
            
            # Pivot data for plotting
            pivot_data = comparison_df.pivot(index='Model', columns='Horizon', values=metric)
            
            # Create heatmap
            sns.heatmap(pivot_data, annot=True, fmt='.2f', cmap='viridis', ax=ax)
            ax.set_title(f'{metric} by Model and Horizon')
            ax.set_xlabel('Prediction Horizon')
            ax.set_ylabel('Model')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Model comparison plot saved to {save_path}")
        
        plt.show()
    
    def get_best_model(self, data: pd.DataFrame, metric: str = 'rmse', 
                      horizon: str = '4h') -> Tuple[str, BaseModel]:
        """Get the best performing model for a specific horizon"""
        if not self.evaluation_results:
            self.evaluate_all_models(data)
        
        best_score = float('inf') if metric in ['rmse', 'mae', 'mape'] else float('-inf')
        best_model_name = None
        
        for model_name, results in self.evaluation_results.items():
            if horizon in results:
                score = results[horizon][metric]
                
                if metric in ['rmse', 'mae', 'mape']:
                    if score < best_score:
                        best_score = score
                        best_model_name = model_name
                else:  # r2, directional_accuracy
                    if score > best_score:
                        best_score = score
                        best_model_name = model_name
        
        if best_model_name is None:
            raise ValueError(f"No model found for horizon {horizon}")
        
        return best_model_name, self.trained_models[best_model_name]
    
    def create_ensemble_prediction(self, data: pd.DataFrame, 
                                 model_names: Optional[List[str]] = None,
                                 weights: Optional[Dict[str, float]] = None) -> Dict[str, np.ndarray]:
        """Create ensemble predictions from multiple models"""
        if model_names is None:
            model_names = list(self.trained_models.keys())
        
        if weights is None:
            # Equal weights
            weights = {name: 1.0 / len(model_names) for name in model_names}
        
        ensemble_predictions = {}
        
        # Get predictions from each model
        model_predictions = {}
        for model_name in model_names:
            if model_name in self.trained_models:
                model = self.trained_models[model_name]
                predictions = model.predict(data)
                model_predictions[model_name] = predictions
        
        # Combine predictions for each horizon
        horizons = ['4h', '16h', '24h']
        for horizon in horizons:
            weighted_preds = []
            total_weight = 0
            
            for model_name, predictions in model_predictions.items():
                if horizon in predictions:
                    weight = weights.get(model_name, 0)
                    weighted_preds.append(predictions[horizon] * weight)
                    total_weight += weight
            
            if weighted_preds and total_weight > 0:
                ensemble_predictions[horizon] = np.sum(weighted_preds, axis=0) / total_weight
        
        return ensemble_predictions
    
    def save_evaluation_results(self, filepath: Path):
        """Save evaluation results to JSON"""
        with open(filepath, 'w') as f:
            json.dump(self.evaluation_results, f, indent=2)
        
        logger.info(f"Evaluation results saved to {filepath}")
    
    def load_evaluation_results(self, filepath: Path):
        """Load evaluation results from JSON"""
        with open(filepath, 'r') as f:
            self.evaluation_results = json.load(f)
        
        logger.info(f"Evaluation results loaded from {filepath}")
    
    def generate_training_report(self, data: pd.DataFrame) -> str:
        """Generate a comprehensive training report"""
        if not self.evaluation_results:
            self.evaluate_all_models(data)
        
        report = []
        report.append("=" * 60)
        report.append("CRYPTOCURRENCY PRICE PREDICTION MODEL TRAINING REPORT")
        report.append("=" * 60)
        report.append(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Data samples: {len(data)}")
        report.append(f"Models trained: {len(self.trained_models)}")
        report.append("")
        
        # Model comparison
        comparison_df = self.compare_models(data)
        
        report.append("MODEL PERFORMANCE SUMMARY")
        report.append("-" * 30)
        
        for horizon in ['4h', '16h', '24h']:
            report.append(f"\n{horizon.upper()} PREDICTION HORIZON:")
            horizon_data = comparison_df[comparison_df['Horizon'] == horizon]
            
            if not horizon_data.empty:
                # Best model for each metric
                best_rmse = horizon_data.loc[horizon_data['RMSE'].idxmin()]
                best_mae = horizon_data.loc[horizon_data['MAE'].idxmin()]
                best_r2 = horizon_data.loc[horizon_data['R2'].idxmax()]
                best_dir_acc = horizon_data.loc[horizon_data['Directional_Accuracy'].idxmax()]
                
                report.append(f"  Best RMSE: {best_rmse['Model']} ({best_rmse['RMSE']:.2f})")
                report.append(f"  Best MAE: {best_mae['Model']} ({best_mae['MAE']:.2f})")
                report.append(f"  Best R²: {best_r2['Model']} ({best_r2['R2']:.4f})")
                report.append(f"  Best Directional Accuracy: {best_dir_acc['Model']} ({best_dir_acc['Directional_Accuracy']:.2f}%)")
        
        report.append("\nDETAILED RESULTS:")
        report.append("-" * 20)
        report.append(comparison_df.to_string(index=False))
        
        return "\n".join(report)

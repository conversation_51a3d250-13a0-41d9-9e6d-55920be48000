"""
Gradient Boosting models for cryptocurrency price prediction
"""
import numpy as np
import pandas as pd
from sklearn.ensemble import GradientBoostingRegressor
from sklearn.model_selection import GridSearchCV
from typing import Dict, List, Tuple, Any
import logging

from .base_model import BaseModel

logger = logging.getLogger(__name__)

class GradientBoostingModel(BaseModel):
    """Gradient Boosting model for price prediction"""
    
    def __init__(self, prediction_horizons: List[int] = [4, 16, 24],
                 n_estimators: int = 100,
                 learning_rate: float = 0.1,
                 max_depth: int = 6,
                 min_samples_split: int = 2,
                 min_samples_leaf: int = 1,
                 subsample: float = 1.0,
                 random_state: int = 42):
        super().__init__("GradientBoosting", prediction_horizons)
        
        self.n_estimators = n_estimators
        self.learning_rate = learning_rate
        self.max_depth = max_depth
        self.min_samples_split = min_samples_split
        self.min_samples_leaf = min_samples_leaf
        self.subsample = subsample
        self.random_state = random_state
        
    def _build_model(self, input_shape: Tuple, horizon: int) -> GradientBoostingRegressor:
        """Build Gradient Boosting model"""
        model = GradientBoostingRegressor(
            n_estimators=self.n_estimators,
            learning_rate=self.learning_rate,
            max_depth=self.max_depth,
            min_samples_split=self.min_samples_split,
            min_samples_leaf=self.min_samples_leaf,
            subsample=self.subsample,
            random_state=self.random_state,
            validation_fraction=0.1,
            n_iter_no_change=10,
            tol=1e-4
        )
        
        return model
    
    def _train_model(self, model: GradientBoostingRegressor, X_train: np.ndarray, y_train: np.ndarray,
                    X_val: np.ndarray, y_val: np.ndarray, horizon: int) -> GradientBoostingRegressor:
        """Train Gradient Boosting model"""
        logger.info(f"Training Gradient Boosting for {horizon}h horizon...")
        
        # Fit the model
        model.fit(X_train, y_train)
        
        logger.info(f"Training completed. Final train score: {model.train_score_[-1]:.6f}")
        
        return model
    
    def _predict(self, model: GradientBoostingRegressor, X: np.ndarray) -> np.ndarray:
        """Make predictions"""
        return model.predict(X)
    
    def get_feature_importance(self) -> Dict[str, np.ndarray]:
        """Get feature importance for each horizon"""
        importance = {}
        
        for horizon in self.prediction_horizons:
            if f'{horizon}h' in self.models:
                model = self.models[f'{horizon}h']
                importance[f'{horizon}h'] = model.feature_importances_
        
        return importance


class OptimizedGradientBoostingModel(GradientBoostingModel):
    """Gradient Boosting model with hyperparameter optimization"""
    
    def __init__(self, prediction_horizons: List[int] = [4, 16, 24],
                 optimize_hyperparams: bool = True, cv_folds: int = 3,
                 random_state: int = 42):
        super().__init__(prediction_horizons=prediction_horizons, random_state=random_state)
        
        self.optimize_hyperparams = optimize_hyperparams
        self.cv_folds = cv_folds
        self.best_params = {}
        
        # Parameter grid for optimization
        self.param_grid = {
            'n_estimators': [100, 200, 300],
            'learning_rate': [0.05, 0.1, 0.2],
            'max_depth': [4, 6, 8],
            'min_samples_split': [2, 5, 10],
            'subsample': [0.8, 0.9, 1.0]
        }
    
    def _build_model(self, input_shape: Tuple, horizon: int) -> GradientBoostingRegressor:
        """Build optimized Gradient Boosting model"""
        if self.optimize_hyperparams:
            # Use grid search for hyperparameter optimization
            base_model = GradientBoostingRegressor(
                random_state=self.random_state,
                validation_fraction=0.1,
                n_iter_no_change=10,
                tol=1e-4
            )
            
            model = GridSearchCV(
                base_model,
                self.param_grid,
                cv=self.cv_folds,
                scoring='neg_mean_squared_error',
                n_jobs=-1,
                verbose=1
            )
        else:
            model = super()._build_model(input_shape, horizon)
        
        return model
    
    def _train_model(self, model, X_train: np.ndarray, y_train: np.ndarray,
                    X_val: np.ndarray, y_val: np.ndarray, horizon: int):
        """Train optimized Gradient Boosting model"""
        logger.info(f"Training optimized Gradient Boosting for {horizon}h horizon...")
        
        if self.optimize_hyperparams:
            # Fit with grid search
            model.fit(X_train, y_train)
            
            # Store best parameters
            self.best_params[f'{horizon}h'] = model.best_params_
            logger.info(f"Best parameters for {horizon}h: {model.best_params_}")
            
            # Return the best estimator
            return model.best_estimator_
        else:
            return super()._train_model(model, X_train, y_train, X_val, y_val, horizon)
    
    def _predict(self, model, X: np.ndarray) -> np.ndarray:
        """Make predictions with optimized model"""
        return model.predict(X)


try:
    import xgboost as xgb
    
    class XGBoostModel(BaseModel):
        """XGBoost model for price prediction"""
        
        def __init__(self, prediction_horizons: List[int] = [4, 16, 24],
                     n_estimators: int = 100,
                     learning_rate: float = 0.1,
                     max_depth: int = 6,
                     min_child_weight: int = 1,
                     subsample: float = 1.0,
                     colsample_bytree: float = 1.0,
                     random_state: int = 42):
            super().__init__("XGBoost", prediction_horizons)
            
            self.n_estimators = n_estimators
            self.learning_rate = learning_rate
            self.max_depth = max_depth
            self.min_child_weight = min_child_weight
            self.subsample = subsample
            self.colsample_bytree = colsample_bytree
            self.random_state = random_state
            
        def _build_model(self, input_shape: Tuple, horizon: int) -> xgb.XGBRegressor:
            """Build XGBoost model"""
            model = xgb.XGBRegressor(
                n_estimators=self.n_estimators,
                learning_rate=self.learning_rate,
                max_depth=self.max_depth,
                min_child_weight=self.min_child_weight,
                subsample=self.subsample,
                colsample_bytree=self.colsample_bytree,
                random_state=self.random_state,
                n_jobs=-1,
                early_stopping_rounds=10,
                eval_metric='rmse'
            )
            
            return model
        
        def _train_model(self, model: xgb.XGBRegressor, X_train: np.ndarray, y_train: np.ndarray,
                        X_val: np.ndarray, y_val: np.ndarray, horizon: int) -> xgb.XGBRegressor:
            """Train XGBoost model"""
            logger.info(f"Training XGBoost for {horizon}h horizon...")
            
            # Fit the model with validation set for early stopping
            model.fit(
                X_train, y_train,
                eval_set=[(X_val, y_val)],
                verbose=False
            )
            
            logger.info(f"Training completed. Best iteration: {model.best_iteration}")
            
            return model
        
        def _predict(self, model: xgb.XGBRegressor, X: np.ndarray) -> np.ndarray:
            """Make predictions"""
            return model.predict(X)
        
        def get_feature_importance(self) -> Dict[str, np.ndarray]:
            """Get feature importance for each horizon"""
            importance = {}
            
            for horizon in self.prediction_horizons:
                if f'{horizon}h' in self.models:
                    model = self.models[f'{horizon}h']
                    importance[f'{horizon}h'] = model.feature_importances_
            
            return importance

except ImportError:
    logger.warning("XGBoost not available. Install with: pip install xgboost")
    
    class XGBoostModel(BaseModel):
        def __init__(self, *args, **kwargs):
            raise ImportError("XGBoost not installed. Install with: pip install xgboost")


try:
    import lightgbm as lgb
    
    class LightGBMModel(BaseModel):
        """LightGBM model for price prediction"""
        
        def __init__(self, prediction_horizons: List[int] = [4, 16, 24],
                     n_estimators: int = 100,
                     learning_rate: float = 0.1,
                     max_depth: int = -1,
                     num_leaves: int = 31,
                     min_child_samples: int = 20,
                     subsample: float = 1.0,
                     colsample_bytree: float = 1.0,
                     random_state: int = 42):
            super().__init__("LightGBM", prediction_horizons)
            
            self.n_estimators = n_estimators
            self.learning_rate = learning_rate
            self.max_depth = max_depth
            self.num_leaves = num_leaves
            self.min_child_samples = min_child_samples
            self.subsample = subsample
            self.colsample_bytree = colsample_bytree
            self.random_state = random_state
            
        def _build_model(self, input_shape: Tuple, horizon: int) -> lgb.LGBMRegressor:
            """Build LightGBM model"""
            model = lgb.LGBMRegressor(
                n_estimators=self.n_estimators,
                learning_rate=self.learning_rate,
                max_depth=self.max_depth,
                num_leaves=self.num_leaves,
                min_child_samples=self.min_child_samples,
                subsample=self.subsample,
                colsample_bytree=self.colsample_bytree,
                random_state=self.random_state,
                n_jobs=-1,
                verbose=-1
            )
            
            return model
        
        def _train_model(self, model: lgb.LGBMRegressor, X_train: np.ndarray, y_train: np.ndarray,
                        X_val: np.ndarray, y_val: np.ndarray, horizon: int) -> lgb.LGBMRegressor:
            """Train LightGBM model"""
            logger.info(f"Training LightGBM for {horizon}h horizon...")
            
            # Fit the model with validation set for early stopping
            model.fit(
                X_train, y_train,
                eval_set=[(X_val, y_val)],
                callbacks=[lgb.early_stopping(10), lgb.log_evaluation(0)]
            )
            
            logger.info(f"Training completed. Best iteration: {model.best_iteration_}")
            
            return model
        
        def _predict(self, model: lgb.LGBMRegressor, X: np.ndarray) -> np.ndarray:
            """Make predictions"""
            return model.predict(X)
        
        def get_feature_importance(self) -> Dict[str, np.ndarray]:
            """Get feature importance for each horizon"""
            importance = {}
            
            for horizon in self.prediction_horizons:
                if f'{horizon}h' in self.models:
                    model = self.models[f'{horizon}h']
                    importance[f'{horizon}h'] = model.feature_importances_
            
            return importance

except ImportError:
    logger.warning("LightGBM not available. Install with: pip install lightgbm")
    
    class LightGBMModel(BaseModel):
        def __init__(self, *args, **kwargs):
            raise ImportError("LightGBM not installed. Install with: pip install lightgbm")

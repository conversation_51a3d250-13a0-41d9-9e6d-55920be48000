"""
Base model class for cryptocurrency price prediction
"""
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Optional, Any
import joblib
import logging
from pathlib import Path
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import json

logger = logging.getLogger(__name__)

class BaseModel(ABC):
    """Abstract base class for prediction models"""
    
    def __init__(self, model_name: str, prediction_horizons: List[int] = [4, 16, 24]):
        self.model_name = model_name
        self.prediction_horizons = prediction_horizons
        self.models = {}  # Store models for each horizon
        self.feature_columns = []
        self.is_trained = False
        self.training_history = {}
        self.model_metadata = {
            'model_name': model_name,
            'prediction_horizons': prediction_horizons,
            'training_date': None,
            'feature_count': 0,
            'training_samples': 0
        }
    
    @abstractmethod
    def _build_model(self, input_shape: Tuple, horizon: int) -> Any:
        """Build the model architecture"""
        pass
    
    @abstractmethod
    def _train_model(self, model: Any, X_train: np.ndarray, y_train: np.ndarray, 
                    X_val: np.ndarray, y_val: np.ndarray, horizon: int) -> Any:
        """Train the model"""
        pass
    
    @abstractmethod
    def _predict(self, model: Any, X: np.ndarray) -> np.ndarray:
        """Make predictions"""
        pass
    
    def prepare_data(self, df: pd.DataFrame) -> Dict[str, Tuple[np.ndarray, np.ndarray]]:
        """Prepare data for training"""
        data = {}
        
        # Get feature columns (exclude target columns and non-numeric columns)
        target_cols = [f'close_target_{h}h' for h in self.prediction_horizons]
        exclude_cols = target_cols + ['timeframe']  # Exclude categorical columns
        self.feature_columns = [col for col in df.columns if col not in exclude_cols and df[col].dtype in ['int64', 'float64']]
        
        for horizon in self.prediction_horizons:
            target_col = f'close_target_{horizon}h'
            
            # Remove rows with NaN targets
            clean_df = df.dropna(subset=[target_col])
            
            X = clean_df[self.feature_columns].values
            y = clean_df[target_col].values
            
            data[f'{horizon}h'] = (X, y)
            
        return data
    
    def split_data(self, X: np.ndarray, y: np.ndarray, 
                  train_ratio: float = 0.7, val_ratio: float = 0.15) -> Tuple:
        """Split data maintaining temporal order"""
        n_samples = len(X)
        train_size = int(n_samples * train_ratio)
        val_size = int(n_samples * val_ratio)
        
        X_train = X[:train_size]
        y_train = y[:train_size]
        
        X_val = X[train_size:train_size + val_size]
        y_val = y[train_size:train_size + val_size]
        
        X_test = X[train_size + val_size:]
        y_test = y[train_size + val_size:]
        
        return X_train, X_val, X_test, y_train, y_val, y_test
    
    def train(self, df: pd.DataFrame, train_ratio: float = 0.7, val_ratio: float = 0.15):
        """Train models for all prediction horizons"""
        logger.info(f"Training {self.model_name} model...")
        
        # Prepare data
        data = self.prepare_data(df)
        
        # Train model for each horizon
        for horizon in self.prediction_horizons:
            logger.info(f"Training model for {horizon}h horizon...")
            
            X, y = data[f'{horizon}h']
            
            # Split data
            X_train, X_val, X_test, y_train, y_val, y_test = self.split_data(X, y, train_ratio, val_ratio)
            
            # Build model
            input_shape = X_train.shape[1:]
            model = self._build_model(input_shape, horizon)
            
            # Train model
            trained_model = self._train_model(model, X_train, y_train, X_val, y_val, horizon)
            
            # Store model
            self.models[f'{horizon}h'] = trained_model
            
            # Evaluate on validation set
            val_pred = self._predict(trained_model, X_val)
            val_metrics = self.calculate_metrics(y_val, val_pred)
            
            logger.info(f"Validation metrics for {horizon}h: {val_metrics}")
            
            # Store training history
            self.training_history[f'{horizon}h'] = {
                'train_samples': len(X_train),
                'val_samples': len(X_val),
                'test_samples': len(X_test),
                'val_metrics': val_metrics
            }
        
        self.is_trained = True
        self.model_metadata['training_date'] = pd.Timestamp.now().isoformat()
        self.model_metadata['feature_count'] = len(self.feature_columns)
        self.model_metadata['training_samples'] = len(X_train)
        
        logger.info(f"{self.model_name} training completed!")
    
    def predict(self, df: pd.DataFrame) -> Dict[str, np.ndarray]:
        """Make predictions for all horizons"""
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")
        
        predictions = {}
        X = df[self.feature_columns].values
        
        for horizon in self.prediction_horizons:
            model = self.models[f'{horizon}h']
            pred = self._predict(model, X)
            predictions[f'{horizon}h'] = pred
        
        return predictions
    
    def calculate_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """Calculate evaluation metrics"""
        metrics = {
            'mae': mean_absolute_error(y_true, y_pred),
            'rmse': np.sqrt(mean_squared_error(y_true, y_pred)),
            'mape': np.mean(np.abs((y_true - y_pred) / y_true)) * 100,
            'r2': r2_score(y_true, y_pred),
            'directional_accuracy': np.mean(np.sign(np.diff(y_true)) == np.sign(np.diff(y_pred))) * 100
        }
        
        return metrics
    
    def evaluate(self, df: pd.DataFrame) -> Dict[str, Dict[str, float]]:
        """Evaluate model on test data"""
        if not self.is_trained:
            raise ValueError("Model must be trained before evaluation")
        
        results = {}
        data = self.prepare_data(df)
        
        for horizon in self.prediction_horizons:
            X, y = data[f'{horizon}h']
            
            # Use last 15% as test data
            test_size = int(len(X) * 0.15)
            X_test = X[-test_size:]
            y_test = y[-test_size:]
            
            # Make predictions
            model = self.models[f'{horizon}h']
            y_pred = self._predict(model, X_test)
            
            # Calculate metrics
            metrics = self.calculate_metrics(y_test, y_pred)
            results[f'{horizon}h'] = metrics
        
        return results
    
    def save_model(self, model_dir: Path):
        """Save trained model"""
        if not self.is_trained:
            raise ValueError("Model must be trained before saving")
        
        model_dir = Path(model_dir)
        model_dir.mkdir(parents=True, exist_ok=True)
        
        # Save models
        for horizon in self.prediction_horizons:
            model_path = model_dir / f"{self.model_name}_{horizon}h.joblib"
            joblib.dump(self.models[f'{horizon}h'], model_path)
        
        # Save metadata
        metadata_path = model_dir / f"{self.model_name}_metadata.json"
        with open(metadata_path, 'w') as f:
            json.dump(self.model_metadata, f, indent=2)
        
        # Save feature columns
        features_path = model_dir / f"{self.model_name}_features.json"
        with open(features_path, 'w') as f:
            json.dump(self.feature_columns, f, indent=2)
        
        # Save training history
        history_path = model_dir / f"{self.model_name}_history.json"
        with open(history_path, 'w') as f:
            json.dump(self.training_history, f, indent=2)
        
        logger.info(f"Model saved to {model_dir}")
    
    def load_model(self, model_dir: Path):
        """Load trained model"""
        model_dir = Path(model_dir)
        
        # Load models
        for horizon in self.prediction_horizons:
            model_path = model_dir / f"{self.model_name}_{horizon}h.joblib"
            if model_path.exists():
                self.models[f'{horizon}h'] = joblib.load(model_path)
        
        # Load metadata
        metadata_path = model_dir / f"{self.model_name}_metadata.json"
        if metadata_path.exists():
            with open(metadata_path, 'r') as f:
                self.model_metadata = json.load(f)
        
        # Load feature columns
        features_path = model_dir / f"{self.model_name}_features.json"
        if features_path.exists():
            with open(features_path, 'r') as f:
                self.feature_columns = json.load(f)
        
        # Load training history
        history_path = model_dir / f"{self.model_name}_history.json"
        if history_path.exists():
            with open(history_path, 'r') as f:
                self.training_history = json.load(f)
        
        self.is_trained = True
        logger.info(f"Model loaded from {model_dir}")
    
    def get_feature_importance(self) -> Dict[str, np.ndarray]:
        """Get feature importance (if supported by the model)"""
        return {}
    
    def get_model_info(self) -> Dict:
        """Get model information"""
        return {
            'model_name': self.model_name,
            'is_trained': self.is_trained,
            'prediction_horizons': self.prediction_horizons,
            'feature_count': len(self.feature_columns),
            'metadata': self.model_metadata,
            'training_history': self.training_history
        }

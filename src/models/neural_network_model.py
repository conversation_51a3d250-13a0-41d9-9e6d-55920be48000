"""
Neural Network model for cryptocurrency price prediction using scikit-learn
"""
import numpy as np
import pandas as pd
from sklearn.neural_network import MLPRegressor
from sklearn.model_selection import GridSearchCV
from sklearn.preprocessing import StandardScaler
from typing import Dict, List, Tuple, Any
import logging

from .base_model import BaseModel

logger = logging.getLogger(__name__)

class NeuralNetworkModel(BaseModel):
    """Multi-layer Perceptron model for price prediction"""
    
    def __init__(self, prediction_horizons: List[int] = [4, 16, 24],
                 hidden_layer_sizes: Tuple = (100, 50),
                 activation: str = 'relu',
                 solver: str = 'adam',
                 alpha: float = 0.001,
                 learning_rate: str = 'constant',
                 learning_rate_init: float = 0.001,
                 max_iter: int = 500,
                 random_state: int = 42):
        super().__init__("NeuralNetwork", prediction_horizons)
        
        self.hidden_layer_sizes = hidden_layer_sizes
        self.activation = activation
        self.solver = solver
        self.alpha = alpha
        self.learning_rate = learning_rate
        self.learning_rate_init = learning_rate_init
        self.max_iter = max_iter
        self.random_state = random_state
        
    def _build_model(self, input_shape: Tuple, horizon: int) -> MLPRegressor:
        """Build Neural Network model"""
        model = MLPRegressor(
            hidden_layer_sizes=self.hidden_layer_sizes,
            activation=self.activation,
            solver=self.solver,
            alpha=self.alpha,
            learning_rate=self.learning_rate,
            learning_rate_init=self.learning_rate_init,
            max_iter=self.max_iter,
            random_state=self.random_state,
            early_stopping=True,
            validation_fraction=0.1,
            n_iter_no_change=10
        )
        
        return model
    
    def _train_model(self, model: MLPRegressor, X_train: np.ndarray, y_train: np.ndarray,
                    X_val: np.ndarray, y_val: np.ndarray, horizon: int) -> MLPRegressor:
        """Train Neural Network model"""
        logger.info(f"Training Neural Network for {horizon}h horizon...")
        
        # Fit the model
        model.fit(X_train, y_train)
        
        logger.info(f"Training completed. Final loss: {model.loss_:.6f}")
        logger.info(f"Number of iterations: {model.n_iter_}")
        
        return model
    
    def _predict(self, model: MLPRegressor, X: np.ndarray) -> np.ndarray:
        """Make predictions"""
        return model.predict(X)


class OptimizedNeuralNetworkModel(NeuralNetworkModel):
    """Neural Network model with hyperparameter optimization"""
    
    def __init__(self, prediction_horizons: List[int] = [4, 16, 24],
                 optimize_hyperparams: bool = True, cv_folds: int = 3,
                 random_state: int = 42):
        super().__init__(prediction_horizons=prediction_horizons, random_state=random_state)
        
        self.optimize_hyperparams = optimize_hyperparams
        self.cv_folds = cv_folds
        self.best_params = {}
        
        # Parameter grid for optimization
        self.param_grid = {
            'hidden_layer_sizes': [(50,), (100,), (100, 50), (100, 100), (200, 100, 50)],
            'activation': ['relu', 'tanh'],
            'alpha': [0.0001, 0.001, 0.01],
            'learning_rate_init': [0.001, 0.01, 0.1]
        }
    
    def _build_model(self, input_shape: Tuple, horizon: int) -> MLPRegressor:
        """Build optimized Neural Network model"""
        if self.optimize_hyperparams:
            # Use grid search for hyperparameter optimization
            base_model = MLPRegressor(
                solver='adam',
                max_iter=300,
                random_state=self.random_state,
                early_stopping=True,
                validation_fraction=0.1,
                n_iter_no_change=10
            )
            
            model = GridSearchCV(
                base_model,
                self.param_grid,
                cv=self.cv_folds,
                scoring='neg_mean_squared_error',
                n_jobs=-1,
                verbose=1
            )
        else:
            model = super()._build_model(input_shape, horizon)
        
        return model
    
    def _train_model(self, model, X_train: np.ndarray, y_train: np.ndarray,
                    X_val: np.ndarray, y_val: np.ndarray, horizon: int):
        """Train optimized Neural Network model"""
        logger.info(f"Training optimized Neural Network for {horizon}h horizon...")
        
        if self.optimize_hyperparams:
            # Fit with grid search
            model.fit(X_train, y_train)
            
            # Store best parameters
            self.best_params[f'{horizon}h'] = model.best_params_
            logger.info(f"Best parameters for {horizon}h: {model.best_params_}")
            
            # Return the best estimator
            return model.best_estimator_
        else:
            return super()._train_model(model, X_train, y_train, X_val, y_val, horizon)
    
    def _predict(self, model, X: np.ndarray) -> np.ndarray:
        """Make predictions with optimized model"""
        return model.predict(X)


class DeepNeuralNetworkModel(BaseModel):
    """Deep Neural Network with multiple hidden layers"""
    
    def __init__(self, prediction_horizons: List[int] = [4, 16, 24],
                 hidden_layers: List[int] = [200, 100, 50, 25],
                 dropout_rate: float = 0.2,
                 activation: str = 'relu',
                 learning_rate_init: float = 0.001,
                 max_iter: int = 1000,
                 random_state: int = 42):
        super().__init__("DeepNeuralNetwork", prediction_horizons)
        
        self.hidden_layers = tuple(hidden_layers)
        self.dropout_rate = dropout_rate
        self.activation = activation
        self.learning_rate_init = learning_rate_init
        self.max_iter = max_iter
        self.random_state = random_state
        
    def _build_model(self, input_shape: Tuple, horizon: int) -> MLPRegressor:
        """Build Deep Neural Network model"""
        model = MLPRegressor(
            hidden_layer_sizes=self.hidden_layers,
            activation=self.activation,
            solver='adam',
            alpha=0.001,  # L2 regularization
            learning_rate='adaptive',
            learning_rate_init=self.learning_rate_init,
            max_iter=self.max_iter,
            random_state=self.random_state,
            early_stopping=True,
            validation_fraction=0.15,
            n_iter_no_change=20,
            tol=1e-6
        )
        
        return model
    
    def _train_model(self, model: MLPRegressor, X_train: np.ndarray, y_train: np.ndarray,
                    X_val: np.ndarray, y_val: np.ndarray, horizon: int) -> MLPRegressor:
        """Train Deep Neural Network model"""
        logger.info(f"Training Deep Neural Network for {horizon}h horizon...")
        logger.info(f"Architecture: {self.hidden_layers}")
        
        # Fit the model
        model.fit(X_train, y_train)
        
        logger.info(f"Training completed. Final loss: {model.loss_:.6f}")
        logger.info(f"Number of iterations: {model.n_iter_}")
        
        return model
    
    def _predict(self, model: MLPRegressor, X: np.ndarray) -> np.ndarray:
        """Make predictions"""
        return model.predict(X)


class EnsembleNeuralNetworkModel(BaseModel):
    """Ensemble of Neural Network models with different architectures"""
    
    def __init__(self, prediction_horizons: List[int] = [4, 16, 24],
                 n_models: int = 3, random_state: int = 42):
        super().__init__("EnsembleNeuralNetwork", prediction_horizons)
        
        self.n_models = n_models
        self.random_state = random_state
        
        # Different architectures for ensemble
        self.model_configs = [
            {'hidden_layer_sizes': (100, 50), 'alpha': 0.001, 'learning_rate_init': 0.001},
            {'hidden_layer_sizes': (200, 100, 50), 'alpha': 0.01, 'learning_rate_init': 0.01},
            {'hidden_layer_sizes': (150, 75), 'alpha': 0.0001, 'learning_rate_init': 0.001}
        ]
    
    def _build_model(self, input_shape: Tuple, horizon: int) -> List[MLPRegressor]:
        """Build ensemble of Neural Network models"""
        models = []
        
        for i in range(self.n_models):
            config = self.model_configs[i % len(self.model_configs)]
            
            model = MLPRegressor(
                activation='relu',
                solver='adam',
                max_iter=500,
                random_state=self.random_state + i,
                early_stopping=True,
                validation_fraction=0.1,
                n_iter_no_change=10,
                **config
            )
            
            models.append(model)
        
        return models
    
    def _train_model(self, models: List[MLPRegressor], X_train: np.ndarray, y_train: np.ndarray,
                    X_val: np.ndarray, y_val: np.ndarray, horizon: int) -> List[MLPRegressor]:
        """Train ensemble of Neural Network models"""
        logger.info(f"Training ensemble of {len(models)} Neural Network models for {horizon}h horizon...")
        
        trained_models = []
        for i, model in enumerate(models):
            logger.info(f"Training model {i+1}/{len(models)}...")
            model.fit(X_train, y_train)
            trained_models.append(model)
            logger.info(f"Model {i+1} completed. Loss: {model.loss_:.6f}")
        
        return trained_models
    
    def _predict(self, models: List[MLPRegressor], X: np.ndarray) -> np.ndarray:
        """Make ensemble predictions"""
        predictions = []
        
        for model in models:
            pred = model.predict(X)
            predictions.append(pred)
        
        # Average predictions
        ensemble_pred = np.mean(predictions, axis=0)
        
        return ensemble_pred

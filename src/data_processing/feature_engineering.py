"""
Advanced feature engineering for cryptocurrency price prediction
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from scipy import stats
from sklearn.decomposition import PCA
from sklearn.feature_selection import SelectKBest, f_regression

logger = logging.getLogger(__name__)

class AdvancedFeatureEngineer:
    """Advanced feature engineering for time series prediction"""
    
    def __init__(self):
        self.pca_transformer = None
        self.feature_selector = None
        
    def create_multi_timeframe_features(self, data_dict: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """Create features from multiple timeframes"""
        logger.info("Creating multi-timeframe features...")
        
        # Use 1h as base timeframe
        base_df = data_dict['1h'].copy()
        
        # Resample other timeframes to hourly and merge
        for timeframe, df in data_dict.items():
            if timeframe == '1h':
                continue
                
            # Resample to hourly frequency
            if timeframe == '15m':
                # Aggregate 15m to 1h
                hourly_agg = df.resample('1H').agg({
                    'open': 'first',
                    'high': 'max',
                    'low': 'min',
                    'close': 'last',
                    'volume': 'sum'
                })
            elif timeframe in ['4h', '1d', '1w', '1M']:
                # Forward fill higher timeframes
                hourly_agg = df.resample('1H').ffill()
            else:
                continue
            
            # Add prefix to column names
            hourly_agg.columns = [f'{timeframe}_{col}' for col in hourly_agg.columns]
            
            # Merge with base dataframe
            base_df = base_df.join(hourly_agg, how='left')
        
        # Forward fill missing values from higher timeframes
        base_df = base_df.fillna(method='ffill')
        
        return base_df
    
    def create_momentum_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create momentum-based features"""
        df_momentum = df.copy()
        
        # Rate of Change (ROC)
        for period in [1, 3, 6, 12, 24]:
            df_momentum[f'roc_{period}'] = df_momentum['close'].pct_change(periods=period)
        
        # Momentum
        for period in [5, 10, 20]:
            df_momentum[f'momentum_{period}'] = df_momentum['close'] / df_momentum['close'].shift(period) - 1
        
        # Williams %R
        for period in [14, 21]:
            high_max = df_momentum['high'].rolling(window=period).max()
            low_min = df_momentum['low'].rolling(window=period).min()
            df_momentum[f'williams_r_{period}'] = -100 * (high_max - df_momentum['close']) / (high_max - low_min)
        
        # Stochastic Oscillator
        for period in [14, 21]:
            high_max = df_momentum['high'].rolling(window=period).max()
            low_min = df_momentum['low'].rolling(window=period).min()
            df_momentum[f'stoch_k_{period}'] = 100 * (df_momentum['close'] - low_min) / (high_max - low_min)
            df_momentum[f'stoch_d_{period}'] = df_momentum[f'stoch_k_{period}'].rolling(window=3).mean()
        
        return df_momentum
    
    def create_volatility_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create volatility-based features"""
        df_vol = df.copy()
        
        # True Range
        df_vol['tr'] = np.maximum(
            df_vol['high'] - df_vol['low'],
            np.maximum(
                abs(df_vol['high'] - df_vol['close'].shift(1)),
                abs(df_vol['low'] - df_vol['close'].shift(1))
            )
        )
        
        # Average True Range (ATR)
        for period in [14, 21]:
            df_vol[f'atr_{period}'] = df_vol['tr'].rolling(window=period).mean()
        
        # Volatility measures
        for period in [5, 10, 20, 50]:
            returns = df_vol['close'].pct_change()
            df_vol[f'volatility_{period}'] = returns.rolling(window=period).std()
            df_vol[f'volatility_ratio_{period}'] = df_vol[f'volatility_{period}'] / df_vol[f'volatility_{period}'].rolling(window=50).mean()
        
        # Garman-Klass volatility
        df_vol['gk_volatility'] = np.log(df_vol['high'] / df_vol['low']) ** 2 / 2 - (2 * np.log(2) - 1) * np.log(df_vol['close'] / df_vol['open']) ** 2
        
        return df_vol
    
    def create_volume_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create volume-based features"""
        df_vol = df.copy()
        
        # Volume moving averages
        for period in [5, 10, 20, 50]:
            df_vol[f'volume_ma_{period}'] = df_vol['volume'].rolling(window=period).mean()
            df_vol[f'volume_ratio_{period}'] = df_vol['volume'] / df_vol[f'volume_ma_{period}']
        
        # Volume Rate of Change
        for period in [1, 3, 6, 12]:
            df_vol[f'volume_roc_{period}'] = df_vol['volume'].pct_change(periods=period)
        
        # On-Balance Volume (OBV)
        price_change = df_vol['close'].diff()
        df_vol['obv'] = (np.sign(price_change) * df_vol['volume']).cumsum()
        
        # Volume-Price Trend (VPT)
        df_vol['vpt'] = (df_vol['volume'] * df_vol['close'].pct_change()).cumsum()
        
        # Accumulation/Distribution Line
        clv = ((df_vol['close'] - df_vol['low']) - (df_vol['high'] - df_vol['close'])) / (df_vol['high'] - df_vol['low'])
        df_vol['ad_line'] = (clv * df_vol['volume']).cumsum()
        
        # Money Flow Index
        typical_price = (df_vol['high'] + df_vol['low'] + df_vol['close']) / 3
        money_flow = typical_price * df_vol['volume']
        
        positive_flow = money_flow.where(typical_price > typical_price.shift(1), 0).rolling(window=14).sum()
        negative_flow = money_flow.where(typical_price < typical_price.shift(1), 0).rolling(window=14).sum()
        
        df_vol['mfi'] = 100 - (100 / (1 + positive_flow / negative_flow))
        
        return df_vol
    
    def create_pattern_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create pattern recognition features"""
        df_pattern = df.copy()
        
        # Candlestick patterns
        body = abs(df_pattern['close'] - df_pattern['open'])
        upper_shadow = df_pattern['high'] - np.maximum(df_pattern['close'], df_pattern['open'])
        lower_shadow = np.minimum(df_pattern['close'], df_pattern['open']) - df_pattern['low']
        
        df_pattern['body_size'] = body / (df_pattern['high'] - df_pattern['low'])
        df_pattern['upper_shadow_ratio'] = upper_shadow / body
        df_pattern['lower_shadow_ratio'] = lower_shadow / body
        
        # Doji pattern
        df_pattern['is_doji'] = (body / (df_pattern['high'] - df_pattern['low']) < 0.1).astype(int)
        
        # Hammer pattern
        df_pattern['is_hammer'] = (
            (lower_shadow > 2 * body) & 
            (upper_shadow < 0.1 * body)
        ).astype(int)
        
        # Shooting star pattern
        df_pattern['is_shooting_star'] = (
            (upper_shadow > 2 * body) & 
            (lower_shadow < 0.1 * body)
        ).astype(int)
        
        # Gap analysis
        df_pattern['gap_up'] = (df_pattern['open'] > df_pattern['high'].shift(1)).astype(int)
        df_pattern['gap_down'] = (df_pattern['open'] < df_pattern['low'].shift(1)).astype(int)
        
        return df_pattern
    
    def create_statistical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create statistical features"""
        df_stats = df.copy()
        
        # Rolling statistics
        for window in [5, 10, 20, 50]:
            # Skewness and Kurtosis
            returns = df_stats['close'].pct_change()
            df_stats[f'skewness_{window}'] = returns.rolling(window=window).skew()
            df_stats[f'kurtosis_{window}'] = returns.rolling(window=window).kurt()
            
            # Quantiles
            df_stats[f'q25_{window}'] = df_stats['close'].rolling(window=window).quantile(0.25)
            df_stats[f'q75_{window}'] = df_stats['close'].rolling(window=window).quantile(0.75)
            df_stats[f'iqr_{window}'] = df_stats[f'q75_{window}'] - df_stats[f'q25_{window}']
            
            # Z-score
            rolling_mean = df_stats['close'].rolling(window=window).mean()
            rolling_std = df_stats['close'].rolling(window=window).std()
            df_stats[f'zscore_{window}'] = (df_stats['close'] - rolling_mean) / rolling_std
        
        return df_stats
    
    def create_fractal_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create fractal and chaos theory features"""
        df_fractal = df.copy()
        
        # Hurst Exponent (simplified)
        def hurst_exponent(ts, max_lag=20):
            lags = range(2, max_lag)
            tau = [np.sqrt(np.std(np.subtract(ts[lag:], ts[:-lag]))) for lag in lags]
            poly = np.polyfit(np.log(lags), np.log(tau), 1)
            return poly[0] * 2.0
        
        # Calculate Hurst exponent for rolling windows
        for window in [50, 100]:
            hurst_values = []
            for i in range(len(df_fractal)):
                if i < window:
                    hurst_values.append(np.nan)
                else:
                    ts = df_fractal['close'].iloc[i-window:i].values
                    try:
                        hurst = hurst_exponent(ts)
                        hurst_values.append(hurst)
                    except:
                        hurst_values.append(np.nan)
            
            df_fractal[f'hurst_{window}'] = hurst_values
        
        return df_fractal
    
    def apply_pca(self, df: pd.DataFrame, n_components: int = 10) -> pd.DataFrame:
        """Apply PCA for dimensionality reduction"""
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        
        # Remove target columns
        feature_cols = [col for col in numeric_cols if not col.endswith('_target_4h') and 
                       not col.endswith('_target_16h') and not col.endswith('_target_24h')]
        
        if self.pca_transformer is None:
            self.pca_transformer = PCA(n_components=n_components)
            pca_features = self.pca_transformer.fit_transform(df[feature_cols].fillna(0))
        else:
            pca_features = self.pca_transformer.transform(df[feature_cols].fillna(0))
        
        # Add PCA features to dataframe
        pca_df = df.copy()
        for i in range(n_components):
            pca_df[f'pca_{i}'] = pca_features[:, i]
        
        return pca_df
    
    def select_best_features(self, df: pd.DataFrame, target_col: str, k: int = 50) -> List[str]:
        """Select best features using statistical tests"""
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        feature_cols = [col for col in numeric_cols if col != target_col]
        
        # Remove rows with NaN in target
        clean_df = df.dropna(subset=[target_col])
        
        if len(clean_df) == 0:
            return feature_cols[:k]
        
        X = clean_df[feature_cols].fillna(0)
        y = clean_df[target_col]
        
        if self.feature_selector is None:
            self.feature_selector = SelectKBest(score_func=f_regression, k=min(k, len(feature_cols)))
            self.feature_selector.fit(X, y)
        
        selected_features = [feature_cols[i] for i in self.feature_selector.get_support(indices=True)]
        
        return selected_features
    
    def create_all_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create all advanced features"""
        logger.info("Creating advanced features...")
        
        # Apply all feature engineering methods
        df_features = self.create_momentum_features(df)
        df_features = self.create_volatility_features(df_features)
        df_features = self.create_volume_features(df_features)
        df_features = self.create_pattern_features(df_features)
        df_features = self.create_statistical_features(df_features)
        df_features = self.create_fractal_features(df_features)
        
        logger.info(f"Created {len(df_features.columns)} total features")
        
        return df_features

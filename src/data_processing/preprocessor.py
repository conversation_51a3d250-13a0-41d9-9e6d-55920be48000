"""
Data preprocessing pipeline for BTC/USDT price prediction
"""
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from sklearn.impute import SimpleImputer
from typing import Dict, List, Tuple, Optional, Union
import logging
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class PreprocessingConfig:
    """Configuration for data preprocessing"""
    sequence_length: int = 60
    prediction_horizons: List[int] = None
    features: List[str] = None
    target: str = 'close'
    scaler_type: str = 'standard'  # 'standard', 'minmax', 'robust'
    handle_outliers: bool = True
    outlier_method: str = 'iqr'  # 'iqr', 'zscore'
    fill_missing: str = 'forward'  # 'forward', 'backward', 'interpolate', 'mean'
    
    def __post_init__(self):
        if self.prediction_horizons is None:
            self.prediction_horizons = [4, 16, 24]
        if self.features is None:
            self.features = ['open', 'high', 'low', 'close', 'volume']

class DataPreprocessor:
    """Comprehensive data preprocessing for time series prediction"""
    
    def __init__(self, config: PreprocessingConfig):
        self.config = config
        self.scalers = {}
        self.feature_columns = []
        self.is_fitted = False
        
    def handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """Handle missing values in the dataset"""
        df_clean = df.copy()
        
        if self.config.fill_missing == 'forward':
            df_clean = df_clean.fillna(method='ffill')
        elif self.config.fill_missing == 'backward':
            df_clean = df_clean.fillna(method='bfill')
        elif self.config.fill_missing == 'interpolate':
            df_clean = df_clean.interpolate(method='linear')
        elif self.config.fill_missing == 'mean':
            imputer = SimpleImputer(strategy='mean')
            numeric_cols = df_clean.select_dtypes(include=[np.number]).columns
            df_clean[numeric_cols] = imputer.fit_transform(df_clean[numeric_cols])
        
        # Drop any remaining NaN values
        initial_len = len(df_clean)
        df_clean = df_clean.dropna()
        dropped = initial_len - len(df_clean)
        
        if dropped > 0:
            logger.warning(f"Dropped {dropped} rows with missing values")
        
        return df_clean
    
    def handle_outliers(self, df: pd.DataFrame) -> pd.DataFrame:
        """Handle outliers in the dataset"""
        if not self.config.handle_outliers:
            return df
        
        df_clean = df.copy()
        
        for col in self.config.features:
            if col not in df_clean.columns:
                continue
                
            if self.config.outlier_method == 'iqr':
                Q1 = df_clean[col].quantile(0.25)
                Q3 = df_clean[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                # Cap outliers instead of removing them
                df_clean[col] = df_clean[col].clip(lower=lower_bound, upper=upper_bound)
                
            elif self.config.outlier_method == 'zscore':
                mean = df_clean[col].mean()
                std = df_clean[col].std()
                lower_bound = mean - 3 * std
                upper_bound = mean + 3 * std
                
                df_clean[col] = df_clean[col].clip(lower=lower_bound, upper=upper_bound)
        
        return df_clean
    
    def create_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create technical indicators as features"""
        df_features = df.copy()
        
        # Moving averages
        for window in [5, 10, 20, 50]:
            df_features[f'ma_{window}'] = df_features['close'].rolling(window=window).mean()
            df_features[f'ma_{window}_ratio'] = df_features['close'] / df_features[f'ma_{window}']
        
        # Exponential moving averages
        for span in [12, 26]:
            df_features[f'ema_{span}'] = df_features['close'].ewm(span=span).mean()
        
        # MACD
        df_features['macd'] = df_features['ema_12'] - df_features['ema_26']
        df_features['macd_signal'] = df_features['macd'].ewm(span=9).mean()
        df_features['macd_histogram'] = df_features['macd'] - df_features['macd_signal']
        
        # RSI
        delta = df_features['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df_features['rsi'] = 100 - (100 / (1 + rs))
        
        # Bollinger Bands
        bb_window = 20
        bb_std = 2
        df_features['bb_middle'] = df_features['close'].rolling(window=bb_window).mean()
        bb_std_dev = df_features['close'].rolling(window=bb_window).std()
        df_features['bb_upper'] = df_features['bb_middle'] + (bb_std_dev * bb_std)
        df_features['bb_lower'] = df_features['bb_middle'] - (bb_std_dev * bb_std)
        df_features['bb_width'] = df_features['bb_upper'] - df_features['bb_lower']
        df_features['bb_position'] = (df_features['close'] - df_features['bb_lower']) / df_features['bb_width']
        
        # Volume indicators
        df_features['volume_ma'] = df_features['volume'].rolling(window=20).mean()
        df_features['volume_ratio'] = df_features['volume'] / df_features['volume_ma']
        
        # Price-based features
        df_features['price_change'] = df_features['close'].pct_change()
        df_features['price_change_ma'] = df_features['price_change'].rolling(window=5).mean()
        df_features['volatility'] = df_features['price_change'].rolling(window=20).std()
        
        # High-Low features
        df_features['hl_ratio'] = df_features['high'] / df_features['low']
        df_features['oc_ratio'] = df_features['open'] / df_features['close']
        
        # Time-based features
        df_features['hour'] = df_features.index.hour
        df_features['day_of_week'] = df_features.index.dayofweek
        df_features['month'] = df_features.index.month
        
        # Cyclical encoding for time features
        df_features['hour_sin'] = np.sin(2 * np.pi * df_features['hour'] / 24)
        df_features['hour_cos'] = np.cos(2 * np.pi * df_features['hour'] / 24)
        df_features['dow_sin'] = np.sin(2 * np.pi * df_features['day_of_week'] / 7)
        df_features['dow_cos'] = np.cos(2 * np.pi * df_features['day_of_week'] / 7)
        df_features['month_sin'] = np.sin(2 * np.pi * df_features['month'] / 12)
        df_features['month_cos'] = np.cos(2 * np.pi * df_features['month'] / 12)
        
        return df_features
    
    def create_lag_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create lagged features for time series prediction"""
        df_lagged = df.copy()
        
        # Create lag features for key indicators
        lag_features = ['close', 'volume', 'rsi', 'macd', 'price_change']
        lag_periods = [1, 2, 3, 6, 12, 24]  # 1h, 2h, 3h, 6h, 12h, 24h
        
        for feature in lag_features:
            if feature in df_lagged.columns:
                for lag in lag_periods:
                    df_lagged[f'{feature}_lag_{lag}'] = df_lagged[feature].shift(lag)
        
        return df_lagged
    
    def scale_features(self, df: pd.DataFrame, fit: bool = True) -> pd.DataFrame:
        """Scale features using the specified scaler"""
        df_scaled = df.copy()
        
        # Select numeric columns for scaling
        numeric_cols = df_scaled.select_dtypes(include=[np.number]).columns.tolist()
        
        # Remove target columns from scaling
        target_cols = [f'{self.config.target}_target_{h}h' for h in self.config.prediction_horizons]
        numeric_cols = [col for col in numeric_cols if col not in target_cols]
        
        if fit:
            # Initialize scaler
            if self.config.scaler_type == 'standard':
                scaler = StandardScaler()
            elif self.config.scaler_type == 'minmax':
                scaler = MinMaxScaler()
            elif self.config.scaler_type == 'robust':
                scaler = RobustScaler()
            else:
                raise ValueError(f"Unknown scaler type: {self.config.scaler_type}")
            
            # Fit and transform
            df_scaled[numeric_cols] = scaler.fit_transform(df_scaled[numeric_cols])
            self.scalers['features'] = scaler
            
        else:
            # Transform only
            if 'features' in self.scalers:
                df_scaled[numeric_cols] = self.scalers['features'].transform(df_scaled[numeric_cols])
        
        return df_scaled
    
    def create_targets(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create target variables for different prediction horizons"""
        df_targets = df.copy()
        
        for horizon in self.config.prediction_horizons:
            target_col = f'{self.config.target}_target_{horizon}h'
            df_targets[target_col] = df_targets[self.config.target].shift(-horizon)
        
        return df_targets
    
    def fit_transform(self, df: pd.DataFrame) -> pd.DataFrame:
        """Fit the preprocessor and transform the data"""
        logger.info("Starting data preprocessing...")
        
        # Step 1: Handle missing values
        df_processed = self.handle_missing_values(df)
        logger.info(f"After handling missing values: {len(df_processed)} records")
        
        # Step 2: Handle outliers
        df_processed = self.handle_outliers(df_processed)
        logger.info("Outliers handled")
        
        # Step 3: Create technical indicators
        df_processed = self.create_technical_indicators(df_processed)
        logger.info("Technical indicators created")
        
        # Step 4: Create lag features
        df_processed = self.create_lag_features(df_processed)
        logger.info("Lag features created")
        
        # Step 5: Create targets
        df_processed = self.create_targets(df_processed)
        logger.info("Target variables created")
        
        # Step 6: Scale features
        df_processed = self.scale_features(df_processed, fit=True)
        logger.info("Features scaled")
        
        # Step 7: Remove rows with NaN values (from lag features and targets)
        initial_len = len(df_processed)
        df_processed = df_processed.dropna()
        final_len = len(df_processed)
        logger.info(f"Final dataset: {final_len} records (removed {initial_len - final_len} rows with NaN)")
        
        # Store feature columns
        target_cols = [f'{self.config.target}_target_{h}h' for h in self.config.prediction_horizons]
        self.feature_columns = [col for col in df_processed.columns if col not in target_cols]
        
        self.is_fitted = True
        return df_processed

    def inverse_transform_target(self, scaled_values: np.ndarray, target_col: str = 'close') -> np.ndarray:
        """Inverse transform target values back to original scale"""
        if not self.is_fitted or 'features' not in self.scalers:
            raise ValueError("Preprocessor must be fitted before inverse transform")

        # Create a dummy array with the same number of features
        n_features = len(self.feature_columns)
        dummy_data = np.zeros((len(scaled_values), n_features))

        # Find the index of the target column in feature columns
        if target_col in self.feature_columns:
            target_idx = self.feature_columns.index(target_col)
            dummy_data[:, target_idx] = scaled_values

            # Inverse transform
            inverse_data = self.scalers['features'].inverse_transform(dummy_data)
            return inverse_data[:, target_idx]
        else:
            # If target column not in features, return as is (might be already unscaled)
            return scaled_values

    def transform(self, df: pd.DataFrame) -> pd.DataFrame:
        """Transform new data using fitted preprocessor"""
        if not self.is_fitted:
            raise ValueError("Preprocessor must be fitted before transform")
        
        # Apply same preprocessing steps without fitting
        df_processed = self.handle_missing_values(df)
        df_processed = self.handle_outliers(df_processed)
        df_processed = self.create_technical_indicators(df_processed)
        df_processed = self.create_lag_features(df_processed)
        df_processed = self.create_targets(df_processed)
        df_processed = self.scale_features(df_processed, fit=False)
        
        return df_processed

"""
Data loading and basic preprocessing utilities for BTC/USDT price data
"""
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import logging
from config import DATA_FILES

logger = logging.getLogger(__name__)

class DataLoader:
    """Load and preprocess BTC/USDT price data from multiple timeframes"""
    
    def __init__(self):
        self.data_files = DATA_FILES
        self.loaded_data = {}
        
    def load_timeframe_data(self, timeframe: str) -> pd.DataFrame:
        """Load data for a specific timeframe"""
        if timeframe not in self.data_files:
            raise ValueError(f"Timeframe {timeframe} not available. Available: {list(self.data_files.keys())}")
        
        file_path = self.data_files[timeframe]
        if not file_path.exists():
            raise FileNotFoundError(f"Data file not found: {file_path}")
        
        logger.info(f"Loading {timeframe} data from {file_path}")
        
        # Load CSV data
        df = pd.read_csv(file_path)
        
        # Convert time column to datetime
        df['time'] = pd.to_datetime(df['time'])
        df.set_index('time', inplace=True)
        
        # Convert price columns to float
        price_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in price_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Sort by time
        df.sort_index(inplace=True)
        
        # Add timeframe identifier
        df['timeframe'] = timeframe
        
        logger.info(f"Loaded {len(df)} records for {timeframe} timeframe")
        return df
    
    def load_all_timeframes(self) -> Dict[str, pd.DataFrame]:
        """Load data for all available timeframes"""
        for timeframe in self.data_files.keys():
            try:
                self.loaded_data[timeframe] = self.load_timeframe_data(timeframe)
            except Exception as e:
                logger.error(f"Failed to load {timeframe} data: {e}")
        
        return self.loaded_data
    
    def get_data_summary(self, timeframe: str) -> Dict:
        """Get summary statistics for a timeframe"""
        if timeframe not in self.loaded_data:
            self.load_timeframe_data(timeframe)
        
        df = self.loaded_data[timeframe]
        
        summary = {
            'timeframe': timeframe,
            'total_records': len(df),
            'date_range': {
                'start': df.index.min(),
                'end': df.index.max()
            },
            'price_stats': {
                'min_price': df['low'].min(),
                'max_price': df['high'].max(),
                'avg_close': df['close'].mean(),
                'price_volatility': df['close'].std()
            },
            'volume_stats': {
                'total_volume': df['volume'].sum(),
                'avg_volume': df['volume'].mean(),
                'volume_volatility': df['volume'].std()
            },
            'missing_values': df.isnull().sum().to_dict(),
            'data_quality': {
                'complete_records': len(df.dropna()),
                'completeness_ratio': len(df.dropna()) / len(df)
            }
        }
        
        return summary
    
    def detect_outliers(self, timeframe: str, method: str = 'iqr') -> pd.DataFrame:
        """Detect outliers in price data"""
        if timeframe not in self.loaded_data:
            self.load_timeframe_data(timeframe)
        
        df = self.loaded_data[timeframe].copy()
        
        if method == 'iqr':
            # Interquartile Range method
            for col in ['open', 'high', 'low', 'close', 'volume']:
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                df[f'{col}_outlier'] = (df[col] < lower_bound) | (df[col] > upper_bound)
        
        elif method == 'zscore':
            # Z-score method
            for col in ['open', 'high', 'low', 'close', 'volume']:
                z_scores = np.abs((df[col] - df[col].mean()) / df[col].std())
                df[f'{col}_outlier'] = z_scores > 3
        
        return df
    
    def calculate_returns(self, timeframe: str) -> pd.DataFrame:
        """Calculate price returns for analysis"""
        if timeframe not in self.loaded_data:
            self.load_timeframe_data(timeframe)
        
        df = self.loaded_data[timeframe].copy()
        
        # Calculate various types of returns
        df['price_change'] = df['close'].diff()
        df['price_change_pct'] = df['close'].pct_change()
        df['log_return'] = np.log(df['close'] / df['close'].shift(1))
        
        # Calculate intraday returns
        df['intraday_return'] = (df['close'] - df['open']) / df['open']
        df['high_low_spread'] = (df['high'] - df['low']) / df['low']
        
        return df

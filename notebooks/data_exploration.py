"""
Comprehensive data exploration and analysis for BTC/USDT price prediction
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

from src.data_processing.data_loader import DataLoader
from config import DATA_FILES

# Set up plotting style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def main():
    """Main data exploration function"""
    print("🚀 Starting BTC/USDT Data Exploration")
    print("=" * 50)
    
    # Initialize data loader
    loader = DataLoader()
    
    # Load all timeframe data
    print("📊 Loading data for all timeframes...")
    all_data = loader.load_all_timeframes()
    
    # Print basic information about each timeframe
    print("\n📈 Data Summary by Timeframe:")
    print("-" * 40)
    
    for timeframe in all_data.keys():
        summary = loader.get_data_summary(timeframe)
        print(f"\n{timeframe.upper()} Timeframe:")
        print(f"  Records: {summary['total_records']:,}")
        print(f"  Date Range: {summary['date_range']['start'].strftime('%Y-%m-%d')} to {summary['date_range']['end'].strftime('%Y-%m-%d')}")
        print(f"  Price Range: ${summary['price_stats']['min_price']:,.2f} - ${summary['price_stats']['max_price']:,.2f}")
        print(f"  Average Close: ${summary['price_stats']['avg_close']:,.2f}")
        print(f"  Price Volatility: ${summary['price_stats']['price_volatility']:,.2f}")
        print(f"  Data Completeness: {summary['data_quality']['completeness_ratio']:.2%}")
    
    # Focus on 1-hour data for detailed analysis
    print("\n🔍 Detailed Analysis of 1-Hour Data")
    print("-" * 40)
    
    df_1h = all_data['1h'].copy()
    
    # Calculate additional metrics
    df_1h_with_returns = loader.calculate_returns('1h')
    
    # Basic statistics
    print("\nBasic Statistics:")
    print(df_1h[['open', 'high', 'low', 'close', 'volume']].describe())
    
    # Price trend analysis
    print("\n📊 Creating visualizations...")
    
    # 1. Price trend over time
    fig = make_subplots(
        rows=3, cols=1,
        subplot_titles=('BTC/USDT Price Over Time', 'Volume', 'Price Returns'),
        vertical_spacing=0.08,
        row_heights=[0.5, 0.25, 0.25]
    )
    
    # Price chart
    fig.add_trace(
        go.Scatter(
            x=df_1h.index,
            y=df_1h['close'],
            mode='lines',
            name='Close Price',
            line=dict(color='#1f77b4', width=1)
        ),
        row=1, col=1
    )
    
    # Volume chart
    fig.add_trace(
        go.Bar(
            x=df_1h.index,
            y=df_1h['volume'],
            name='Volume',
            marker_color='rgba(255, 165, 0, 0.7)'
        ),
        row=2, col=1
    )
    
    # Returns chart
    fig.add_trace(
        go.Scatter(
            x=df_1h_with_returns.index,
            y=df_1h_with_returns['price_change_pct'] * 100,
            mode='lines',
            name='Price Returns (%)',
            line=dict(color='#d62728', width=1)
        ),
        row=3, col=1
    )
    
    fig.update_layout(
        title='BTC/USDT Comprehensive Analysis (1-Hour Data)',
        height=800,
        showlegend=True
    )
    
    fig.update_xaxes(title_text="Date", row=3, col=1)
    fig.update_yaxes(title_text="Price (USDT)", row=1, col=1)
    fig.update_yaxes(title_text="Volume", row=2, col=1)
    fig.update_yaxes(title_text="Returns (%)", row=3, col=1)
    
    # Save the plot
    fig.write_html("btc_analysis_overview.html")
    print("💾 Saved comprehensive analysis chart as 'btc_analysis_overview.html'")
    
    # 2. Distribution analysis
    print("\n📊 Analyzing price distributions...")
    
    # Create distribution plots
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('BTC/USDT Price Distribution Analysis', fontsize=16)
    
    # Price distribution
    axes[0, 0].hist(df_1h['close'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 0].set_title('Close Price Distribution')
    axes[0, 0].set_xlabel('Price (USDT)')
    axes[0, 0].set_ylabel('Frequency')
    
    # Volume distribution
    axes[0, 1].hist(df_1h['volume'], bins=50, alpha=0.7, color='lightcoral', edgecolor='black')
    axes[0, 1].set_title('Volume Distribution')
    axes[0, 1].set_xlabel('Volume')
    axes[0, 1].set_ylabel('Frequency')
    
    # Returns distribution
    returns = df_1h_with_returns['price_change_pct'].dropna()
    axes[1, 0].hist(returns * 100, bins=50, alpha=0.7, color='lightgreen', edgecolor='black')
    axes[1, 0].set_title('Price Returns Distribution')
    axes[1, 0].set_xlabel('Returns (%)')
    axes[1, 0].set_ylabel('Frequency')
    
    # Log returns distribution
    log_returns = df_1h_with_returns['log_return'].dropna()
    axes[1, 1].hist(log_returns, bins=50, alpha=0.7, color='gold', edgecolor='black')
    axes[1, 1].set_title('Log Returns Distribution')
    axes[1, 1].set_xlabel('Log Returns')
    axes[1, 1].set_ylabel('Frequency')
    
    plt.tight_layout()
    plt.savefig('btc_distributions.png', dpi=300, bbox_inches='tight')
    print("💾 Saved distribution analysis as 'btc_distributions.png'")
    plt.close()
    
    # 3. Correlation analysis
    print("\n🔗 Analyzing correlations...")
    
    # Calculate correlations between different timeframes
    correlation_data = {}
    for tf in ['15m', '1h', '4h', '1d']:
        if tf in all_data:
            # Resample to daily frequency for comparison
            daily_data = all_data[tf].resample('D').last()
            correlation_data[f'{tf}_close'] = daily_data['close']
            correlation_data[f'{tf}_volume'] = daily_data['volume']
    
    corr_df = pd.DataFrame(correlation_data).corr()
    
    # Plot correlation heatmap
    plt.figure(figsize=(12, 8))
    sns.heatmap(corr_df, annot=True, cmap='coolwarm', center=0, 
                square=True, linewidths=0.5, cbar_kws={"shrink": .8})
    plt.title('Correlation Matrix: Multi-Timeframe Analysis')
    plt.tight_layout()
    plt.savefig('correlation_matrix.png', dpi=300, bbox_inches='tight')
    print("💾 Saved correlation matrix as 'correlation_matrix.png'")
    plt.close()
    
    # 4. Volatility analysis
    print("\n📈 Analyzing volatility patterns...")
    
    # Calculate rolling volatility
    df_1h_with_returns['volatility_24h'] = df_1h_with_returns['log_return'].rolling(window=24).std() * np.sqrt(24)
    df_1h_with_returns['volatility_168h'] = df_1h_with_returns['log_return'].rolling(window=168).std() * np.sqrt(168)
    
    # Plot volatility
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
    
    # Price and volatility
    ax1.plot(df_1h.index, df_1h['close'], label='Close Price', color='blue', alpha=0.7)
    ax1.set_ylabel('Price (USDT)', color='blue')
    ax1.tick_params(axis='y', labelcolor='blue')
    ax1.set_title('BTC/USDT Price and Volatility Analysis')
    
    ax1_twin = ax1.twinx()
    ax1_twin.plot(df_1h_with_returns.index, df_1h_with_returns['volatility_24h'], 
                  label='24h Volatility', color='red', alpha=0.7)
    ax1_twin.set_ylabel('Volatility', color='red')
    ax1_twin.tick_params(axis='y', labelcolor='red')
    
    # Volume analysis
    ax2.bar(df_1h.index, df_1h['volume'], alpha=0.6, color='orange', width=0.02)
    ax2.set_ylabel('Volume')
    ax2.set_xlabel('Date')
    ax2.set_title('Trading Volume Over Time')
    
    plt.tight_layout()
    plt.savefig('volatility_analysis.png', dpi=300, bbox_inches='tight')
    print("💾 Saved volatility analysis as 'volatility_analysis.png'")
    plt.close()
    
    # 5. Summary statistics
    print("\n📊 Summary Statistics:")
    print("-" * 30)
    
    summary_stats = {
        'Total Trading Days': len(df_1h) / 24,
        'Average Daily Volume': df_1h['volume'].sum() / (len(df_1h) / 24),
        'Price Appreciation': ((df_1h['close'].iloc[-1] / df_1h['close'].iloc[0]) - 1) * 100,
        'Maximum Drawdown': ((df_1h['close'].cummax() - df_1h['close']) / df_1h['close'].cummax()).max() * 100,
        'Average Daily Return': returns.mean() * 24 * 100,
        'Daily Volatility': returns.std() * np.sqrt(24) * 100,
        'Sharpe Ratio (approx)': (returns.mean() * 24) / (returns.std() * np.sqrt(24)) if returns.std() > 0 else 0
    }
    
    for key, value in summary_stats.items():
        if isinstance(value, float):
            print(f"{key}: {value:.2f}")
        else:
            print(f"{key}: {value}")
    
    print("\n✅ Data exploration completed!")
    print("📁 Generated files:")
    print("  - btc_analysis_overview.html (interactive chart)")
    print("  - btc_distributions.png (distribution analysis)")
    print("  - correlation_matrix.png (correlation heatmap)")
    print("  - volatility_analysis.png (volatility patterns)")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Fetch historical BTC/USDT data from 2018-2020 for all timeframes
This script will fetch data to extend the existing dataset backwards in time
"""

import os
import subprocess
import json
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
import time

class HistoricalDataFetcher2018:
    def __init__(self):
        self.data_dir = Path("data")
        self.fetcher_dir = Path("historical-data-fetcher")
        self.timeframes = ['15m', '1h', '4h', '1d', '1w', '1M']
        
        # Date ranges for fetching
        self.start_date = "2018-01-01"
        self.end_date = "2019-12-31"  # End of 2019, so we don't overlap with existing 2020 data
        
    def check_prerequisites(self):
        """Check if Node.js and the data fetcher are available"""
        print("🔍 Checking prerequisites...")
        
        # Check if Node.js is available
        try:
            result = subprocess.run(['node', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Node.js version: {result.stdout.strip()}")
            else:
                print("❌ Node.js not found. Please install Node.js 18+")
                return False
        except FileNotFoundError:
            print("❌ Node.js not found. Please install Node.js 18+")
            return False
        
        # Check if data fetcher directory exists
        if not self.fetcher_dir.exists():
            print(f"❌ Data fetcher directory not found: {self.fetcher_dir}")
            return False
        
        # Check if npm dependencies are installed
        node_modules = self.fetcher_dir / "node_modules"
        if not node_modules.exists():
            print("📦 Installing npm dependencies...")
            try:
                subprocess.run(['npm', 'install'], cwd=self.fetcher_dir, check=True)
                print("✅ npm dependencies installed")
            except subprocess.CalledProcessError:
                print("❌ Failed to install npm dependencies")
                return False
        else:
            print("✅ npm dependencies already installed")
        
        return True
    
    def create_fetch_script(self, timeframe):
        """Create a custom fetch script for a specific timeframe"""
        script_content = f"""
import {{ DataFetcher }} from './services/dataFetcher.js';
import {{ CsvExporter }} from './services/csvExporter.js';
import {{ defaultFetchConfig, FetchConfig }} from './config/config.js';

async function fetchData() {{
  const dataFetcher = new DataFetcher();
  const csvExporter = new CsvExporter();
  
  const config = {{
    ...defaultFetchConfig,
    symbol: 'BTCUSDT',
    interval: '{timeframe}',
    startDate: new Date('{self.start_date}'),
    endDate: new Date('{self.end_date}'),
    outputFile: '{timeframe}_01012018_31122019.csv'
  }};
  
  try {{
    console.log(`🎯 Fetching {{config.interval}} data from {{config.startDate.toISOString()}} to {{config.endDate.toISOString()}}`);
    
    const historicalData = await dataFetcher.fetchHistoricalData(config);
    
    if (historicalData.length === 0) {{
      console.log('⚠️ No data was fetched');
      return;
    }}
    
    await csvExporter.exportToCSV(historicalData, config.outputFile);
    
    console.log(`✅ {{config.interval}} data fetched successfully!`);
    console.log(`📁 Output file: {{config.outputFile}}`);
    console.log(`📊 Total records: {{historicalData.length}}`);
    
  }} catch (error) {{
    console.error(`❌ Error fetching {{config.interval}} data:`, error);
    process.exit(1);
  }}
}}

fetchData().catch(console.error);
"""
        
        script_path = self.fetcher_dir / f"fetch_{timeframe}_2018_2019.js"
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        return script_path
    
    def fetch_timeframe_data(self, timeframe):
        """Fetch data for a specific timeframe"""
        print(f"\n📊 Fetching {timeframe} data from {self.start_date} to {self.end_date}...")
        
        # Create custom fetch script
        script_path = self.create_fetch_script(timeframe)
        
        try:
            # Run the fetch script
            result = subprocess.run(
                ['node', script_path.name],
                cwd=self.fetcher_dir,
                capture_output=True,
                text=True,
                timeout=600  # 10 minute timeout
            )
            
            if result.returncode == 0:
                print(f"✅ {timeframe} data fetched successfully")
                print(result.stdout)
                
                # Move the file to the data directory
                source_file = self.fetcher_dir / f"{timeframe}_01012018_31122019.csv"
                target_file = self.data_dir / f"{timeframe}_01012018_31122019.csv"
                
                if source_file.exists():
                    source_file.rename(target_file)
                    print(f"📁 File moved to: {target_file}")
                    return True
                else:
                    print(f"⚠️ Output file not found: {source_file}")
                    return False
            else:
                print(f"❌ Failed to fetch {timeframe} data")
                print("STDOUT:", result.stdout)
                print("STDERR:", result.stderr)
                return False
                
        except subprocess.TimeoutExpired:
            print(f"⏰ Timeout while fetching {timeframe} data")
            return False
        except Exception as e:
            print(f"❌ Error fetching {timeframe} data: {e}")
            return False
        finally:
            # Clean up the script file
            if script_path.exists():
                script_path.unlink()
    
    def merge_datasets(self, timeframe):
        """Merge the new 2018-2019 data with existing 2020-2025 data"""
        print(f"\n🔗 Merging {timeframe} datasets...")
        
        old_file = self.data_dir / f"{timeframe}_01012018_31122019.csv"
        existing_file = self.data_dir / f"{timeframe}_3112020_3062025.csv"
        merged_file = self.data_dir / f"{timeframe}_01012018_3062025.csv"
        
        if not old_file.exists():
            print(f"⚠️ New data file not found: {old_file}")
            return False
        
        if not existing_file.exists():
            print(f"⚠️ Existing data file not found: {existing_file}")
            return False
        
        try:
            # Load both datasets
            print(f"📖 Loading {old_file}")
            df_old = pd.read_csv(old_file)
            df_old['time'] = pd.to_datetime(df_old['time'])
            
            print(f"📖 Loading {existing_file}")
            df_existing = pd.read_csv(existing_file)
            df_existing['time'] = pd.to_datetime(df_existing['time'])
            
            # Combine datasets
            df_combined = pd.concat([df_old, df_existing], ignore_index=True)
            
            # Sort by time and remove duplicates
            df_combined = df_combined.sort_values('time').drop_duplicates(subset=['time']).reset_index(drop=True)
            
            # Save merged dataset
            df_combined.to_csv(merged_file, index=False)
            
            print(f"✅ Merged dataset saved: {merged_file}")
            print(f"📊 Total records: {len(df_combined):,}")
            print(f"📅 Date range: {df_combined['time'].min()} to {df_combined['time'].max()}")
            print(f"💰 Price range: ${df_combined['close'].min():.2f} to ${df_combined['close'].max():.2f}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error merging datasets: {e}")
            return False
    
    def run(self):
        """Run the complete data fetching process"""
        print("🚀 Historical Data Fetcher (2018-2020)")
        print("=" * 50)
        print(f"📅 Fetching data from {self.start_date} to {self.end_date}")
        print(f"📊 Timeframes: {', '.join(self.timeframes)}")
        print()
        
        # Check prerequisites
        if not self.check_prerequisites():
            print("❌ Prerequisites not met. Exiting.")
            return False
        
        # Create data directory if it doesn't exist
        self.data_dir.mkdir(exist_ok=True)
        
        successful_fetches = []
        failed_fetches = []
        
        # Fetch data for each timeframe
        for timeframe in self.timeframes:
            print(f"\n{'='*20} {timeframe.upper()} {'='*20}")
            
            if self.fetch_timeframe_data(timeframe):
                successful_fetches.append(timeframe)
                
                # Merge with existing data
                if self.merge_datasets(timeframe):
                    print(f"✅ {timeframe} data successfully integrated")
                else:
                    print(f"⚠️ {timeframe} data fetched but merge failed")
            else:
                failed_fetches.append(timeframe)
            
            # Add delay between requests to avoid rate limiting
            if timeframe != self.timeframes[-1]:  # Don't wait after the last one
                print("⏳ Waiting 30 seconds to avoid rate limiting...")
                time.sleep(30)
        
        # Summary
        print(f"\n{'='*50}")
        print("📊 SUMMARY")
        print(f"{'='*50}")
        print(f"✅ Successful: {len(successful_fetches)} timeframes")
        if successful_fetches:
            print(f"   {', '.join(successful_fetches)}")
        
        print(f"❌ Failed: {len(failed_fetches)} timeframes")
        if failed_fetches:
            print(f"   {', '.join(failed_fetches)}")
        
        if successful_fetches:
            print(f"\n🎉 Data fetching completed!")
            print(f"📁 New merged files available in {self.data_dir}/")
            print(f"🔄 You can now retrain models with the extended dataset")
            print(f"💡 Run: python3 train_models.py")
            return True
        else:
            print(f"\n❌ No data was successfully fetched")
            return False

def main():
    fetcher = HistoricalDataFetcher2018()
    success = fetcher.run()
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())

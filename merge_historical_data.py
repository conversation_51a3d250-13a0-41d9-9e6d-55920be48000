#!/usr/bin/env python3
"""
Merge historical data from 2018-2020 with existing 2020-2025 data
This creates extended datasets covering 2018-2025 for improved model training
"""

import pandas as pd
import numpy as np
from pathlib import Path
import shutil
from datetime import datetime

class DataMerger:
    def __init__(self):
        self.data_dir = Path("data")
        self.fetcher_dir = Path("historical-data-fetcher")
        self.timeframes = ['15m', '1h', '4h', '1d', '1w', '1M']
        
    def check_files(self):
        """Check which files are available for merging"""
        print("🔍 Checking available data files...")
        
        available_merges = []
        
        for timeframe in self.timeframes:
            # Check both locations for the 2018-2019 data files
            old_file_fetcher = self.fetcher_dir / f"{timeframe}_01012018_31122019.csv"
            old_file_data = self.data_dir / f"{timeframe}_01012018_31122019.csv"

            # Use whichever file exists
            if old_file_data.exists():
                old_file = old_file_data
            elif old_file_fetcher.exists():
                old_file = old_file_fetcher
            else:
                old_file = old_file_data  # Default for error reporting

            existing_file = self.data_dir / f"{timeframe}_3112020_3062025.csv"
            
            old_exists = old_file.exists()
            existing_exists = existing_file.exists()
            
            print(f"📊 {timeframe}:")
            print(f"   2018-2019 data: {'✅' if old_exists else '❌'} {old_file}")
            print(f"   2020-2025 data: {'✅' if existing_exists else '❌'} {existing_file}")
            
            if old_exists and existing_exists:
                available_merges.append(timeframe)
                print(f"   🔗 Ready for merge")
            else:
                print(f"   ⚠️  Cannot merge (missing files)")
            print()
        
        return available_merges
    
    def analyze_data_quality(self, df, timeframe, period):
        """Analyze data quality and provide statistics"""
        print(f"📊 {period} data analysis:")
        print(f"   Records: {len(df):,}")
        print(f"   Date range: {df['time'].min()} to {df['time'].max()}")
        print(f"   Price range: ${df['close'].min():.2f} to ${df['close'].max():.2f}")
        
        # Check for missing values
        missing_values = df.isnull().sum().sum()
        if missing_values > 0:
            print(f"   ⚠️  Missing values: {missing_values}")
        else:
            print(f"   ✅ No missing values")
        
        # Check for duplicates
        duplicates = df.duplicated(subset=['time']).sum()
        if duplicates > 0:
            print(f"   ⚠️  Duplicate timestamps: {duplicates}")
        else:
            print(f"   ✅ No duplicate timestamps")
        
        # Check time continuity
        df_sorted = df.sort_values('time')
        time_diffs = df_sorted['time'].diff().dropna()
        
        if timeframe == '15m':
            expected_diff = pd.Timedelta(minutes=15)
        elif timeframe == '1h':
            expected_diff = pd.Timedelta(hours=1)
        elif timeframe == '4h':
            expected_diff = pd.Timedelta(hours=4)
        elif timeframe == '1d':
            expected_diff = pd.Timedelta(days=1)
        elif timeframe == '1w':
            expected_diff = pd.Timedelta(weeks=1)
        elif timeframe == '1M':
            expected_diff = pd.Timedelta(days=30)  # Approximate
        
        # Count gaps (allowing some tolerance for monthly data)
        if timeframe == '1M':
            gaps = (time_diffs > expected_diff * 1.5).sum()
        else:
            gaps = (time_diffs > expected_diff * 1.1).sum()
        
        if gaps > 0:
            print(f"   ⚠️  Time gaps detected: {gaps}")
        else:
            print(f"   ✅ Time continuity good")
        
        print()
    
    def merge_timeframe_data(self, timeframe):
        """Merge data for a specific timeframe"""
        print(f"🔗 Merging {timeframe} data...")

        # Check both locations for the 2018-2019 data files
        old_file_fetcher = self.fetcher_dir / f"{timeframe}_01012018_31122019.csv"
        old_file_data = self.data_dir / f"{timeframe}_01012018_31122019.csv"

        # Use whichever file exists
        if old_file_data.exists():
            old_file = old_file_data
        elif old_file_fetcher.exists():
            old_file = old_file_fetcher
        else:
            print(f"❌ 2018-2019 data file not found for {timeframe}")
            return False

        existing_file = self.data_dir / f"{timeframe}_3112020_3062025.csv"
        merged_file = self.data_dir / f"{timeframe}_01012018_3062025.csv"
        backup_file = self.data_dir / f"{timeframe}_3112020_3062025_backup.csv"
        
        try:
            # Load datasets
            print(f"📖 Loading 2018-2019 data from {old_file}")
            df_old = pd.read_csv(old_file)
            df_old['time'] = pd.to_datetime(df_old['time'])

            print(f"📖 Loading 2020-2025 data from {existing_file}")
            df_existing = pd.read_csv(existing_file)
            df_existing['time'] = pd.to_datetime(df_existing['time'])

            # Handle timezone issues - convert both to timezone-naive UTC
            if df_old['time'].dt.tz is not None:
                df_old['time'] = df_old['time'].dt.tz_convert('UTC').dt.tz_localize(None)

            if df_existing['time'].dt.tz is not None:
                df_existing['time'] = df_existing['time'].dt.tz_convert('UTC').dt.tz_localize(None)
            
            # Analyze data quality
            self.analyze_data_quality(df_old, timeframe, "2018-2019")
            self.analyze_data_quality(df_existing, timeframe, "2020-2025")
            
            # Check for overlap
            old_max = df_old['time'].max()
            existing_min = df_existing['time'].min()
            
            print(f"🔍 Checking for overlap:")
            print(f"   2018-2019 ends: {old_max}")
            print(f"   2020-2025 starts: {existing_min}")
            
            if old_max >= existing_min:
                print(f"   ⚠️  Overlap detected! Removing overlapping records...")
                # Remove overlapping records from old data
                df_old = df_old[df_old['time'] < existing_min]
                print(f"   ✅ Overlap resolved. 2018-2019 data now has {len(df_old):,} records")
            else:
                print(f"   ✅ No overlap detected")
            
            # Combine datasets
            print(f"🔄 Combining datasets...")
            df_combined = pd.concat([df_old, df_existing], ignore_index=True)
            
            # Sort by time and remove any remaining duplicates
            df_combined = df_combined.sort_values('time').drop_duplicates(subset=['time']).reset_index(drop=True)
            
            # Final quality check
            print(f"📊 Combined dataset analysis:")
            self.analyze_data_quality(df_combined, timeframe, "2018-2025 Combined")
            
            # Create backup of original file
            print(f"💾 Creating backup: {backup_file}")
            shutil.copy2(existing_file, backup_file)
            
            # Save merged dataset
            print(f"💾 Saving merged dataset: {merged_file}")
            df_combined.to_csv(merged_file, index=False)
            
            # Calculate improvement statistics
            old_records = len(df_existing)
            new_records = len(df_combined)
            improvement = ((new_records - old_records) / old_records) * 100
            
            print(f"✅ {timeframe} merge completed successfully!")
            print(f"📈 Dataset improvement:")
            print(f"   Before: {old_records:,} records")
            print(f"   After: {new_records:,} records")
            print(f"   Increase: +{new_records - old_records:,} records ({improvement:.1f}%)")
            print()
            
            return True
            
        except Exception as e:
            print(f"❌ Error merging {timeframe} data: {e}")
            return False
    
    def update_training_info(self):
        """Update the training data info file"""
        try:
            info_file = Path("TRAINING_DATA_INFO.md")
            if info_file.exists():
                # Read current content
                with open(info_file, 'r') as f:
                    content = f.read()
                
                # Update the content with new date range
                updated_content = content.replace(
                    "January 31, 2020 17:00 UTC to June 24, 2025 15:00 UTC",
                    "January 1, 2018 00:00 UTC to June 24, 2025 15:00 UTC"
                ).replace(
                    "5.4 years of complete market data",
                    "7.5 years of complete market data"
                ).replace(
                    "47,271 records",
                    "Extended dataset with 2018-2020 historical data"
                )
                
                # Write updated content
                with open(info_file, 'w') as f:
                    f.write(updated_content)
                
                print(f"✅ Updated {info_file}")
        except Exception as e:
            print(f"⚠️  Could not update training info: {e}")
    
    def run(self):
        """Run the complete data merging process"""
        print("🔗 Historical Data Merger (2018-2025)")
        print("=" * 50)
        print("This script will merge 2018-2020 data with existing 2020-2025 data")
        print()
        
        # Check available files
        available_merges = self.check_files()
        
        if not available_merges:
            print("❌ No files available for merging.")
            print("💡 Please run the data fetcher first:")
            print("   cd historical-data-fetcher")
            print("   node fetch_2018_2020.js")
            return False
        
        print(f"🎯 Ready to merge {len(available_merges)} timeframes: {', '.join(available_merges)}")
        print()
        
        # Confirm with user
        response = input("Continue with merging? (y/N): ").strip().lower()
        if response != 'y':
            print("❌ Merge cancelled by user")
            return False
        
        successful_merges = []
        failed_merges = []
        
        # Merge each timeframe
        for timeframe in available_merges:
            print(f"\n{'='*20} {timeframe.upper()} {'='*20}")
            
            if self.merge_timeframe_data(timeframe):
                successful_merges.append(timeframe)
            else:
                failed_merges.append(timeframe)
        
        # Summary
        print(f"\n{'='*50}")
        print("📊 MERGE SUMMARY")
        print(f"{'='*50}")
        print(f"✅ Successful: {len(successful_merges)} timeframes")
        if successful_merges:
            print(f"   {', '.join(successful_merges)}")
        
        print(f"❌ Failed: {len(failed_merges)} timeframes")
        if failed_merges:
            print(f"   {', '.join(failed_merges)}")
        
        if successful_merges:
            print(f"\n🎉 Data merging completed!")
            print(f"📁 Extended datasets available in {self.data_dir}/")
            print(f"📈 Your models can now train on 7.5 years of data (2018-2025)")
            print(f"🔄 Next step: Retrain models with extended dataset")
            print(f"💡 Run: python3 train_models.py")
            
            # Update training info
            self.update_training_info()
            
            return True
        else:
            print(f"\n❌ No data was successfully merged")
            return False

def main():
    merger = DataMerger()
    success = merger.run()
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())

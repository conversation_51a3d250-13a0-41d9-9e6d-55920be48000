# Maintenance Guide

This guide covers ongoing maintenance tasks for the BTC/USDT Price Prediction System.

## 🔄 Regular Maintenance Tasks

### Daily Tasks

1. **Monitor System Health**
   ```bash
   # Check API status
   curl http://localhost:8000/health
   
   # Check system resources
   python3 -c "
   import psutil
   print(f'CPU: {psutil.cpu_percent()}%')
   print(f'Memory: {psutil.virtual_memory().percent}%')
   print(f'Disk: {psutil.disk_usage(\"/\").percent}%')
   "
   ```

2. **Review Logs**
   ```bash
   # Check for errors in the last 24 hours
   tail -n 1000 logs/app.log | grep -i error
   
   # Monitor prediction accuracy
   grep "prediction" logs/app.log | tail -20
   ```

3. **Validate Predictions**
   ```bash
   # Test all models
   python3 test_system.py
   ```

### Weekly Tasks

1. **Model Performance Review**
   ```bash
   # Generate performance report
   python3 -c "
   import json
   with open('evaluation_results.json', 'r') as f:
       results = json.load(f)
   
   for model, metrics in results.items():
       print(f'{model}:')
       for horizon, scores in metrics.items():
           print(f'  {horizon}: R²={scores[\"r2\"]:.4f}, RMSE={scores[\"rmse\"]:.2f}')
   "
   ```

2. **Data Quality Check**
   ```bash
   # Check for data gaps or anomalies
   python3 -c "
   from src.data_processing.data_loader import DataLoader
   loader = DataLoader()
   df = loader.load_timeframe_data('1h')
   
   # Check for missing data
   missing_hours = df.index.to_series().diff().dt.total_seconds() / 3600
   gaps = missing_hours[missing_hours > 1.1]  # More than 1.1 hours
   
   if len(gaps) > 0:
       print(f'Found {len(gaps)} data gaps')
       print(gaps.head())
   else:
       print('No significant data gaps found')
   "
   ```

3. **Backup Verification**
   ```bash
   # Verify backup integrity
   ls -la backups/
   
   # Test model restoration
   cp models/NeuralNetwork_4h.joblib models/NeuralNetwork_4h.joblib.backup
   # ... test restoration process
   ```

### Monthly Tasks

1. **Model Retraining**
   ```bash
   # Full model retraining with latest data
   python3 train_models.py
   
   # Compare new vs old performance
   python3 -c "
   import json
   import pandas as pd
   
   # Load old results
   with open('evaluation_results_old.json', 'r') as f:
       old_results = json.load(f)
   
   # Load new results  
   with open('evaluation_results.json', 'r') as f:
       new_results = json.load(f)
   
   # Compare performance
   for model in old_results:
       if model in new_results:
           for horizon in old_results[model]:
               old_r2 = old_results[model][horizon]['r2']
               new_r2 = new_results[model][horizon]['r2']
               improvement = new_r2 - old_r2
               print(f'{model} {horizon}: {improvement:+.4f} R² change')
   "
   ```

2. **System Updates**
   ```bash
   # Update Python packages
   pip list --outdated
   pip install --upgrade -r requirements.txt
   
   # Update system packages (if on Linux)
   sudo apt update && sudo apt upgrade
   ```

3. **Performance Optimization**
   ```bash
   # Profile API performance
   python3 -c "
   import time
   import requests
   
   # Test prediction latency
   start_time = time.time()
   response = requests.post('http://localhost:8000/predict', 
                           json={'model_name': 'NeuralNetwork'})
   latency = time.time() - start_time
   
   print(f'Prediction latency: {latency:.3f} seconds')
   if latency > 5.0:
       print('⚠️  High latency detected')
   "
   ```

## 🔧 Troubleshooting Common Issues

### API Not Responding

1. **Check Process Status**
   ```bash
   ps aux | grep python
   netstat -tlnp | grep 8000
   ```

2. **Restart API**
   ```bash
   # Kill existing process
   pkill -f "python3 run_api.py"
   
   # Start new process
   nohup python3 run_api.py > logs/api.log 2>&1 &
   ```

3. **Check Dependencies**
   ```bash
   python3 -c "
   import sys
   required = ['fastapi', 'uvicorn', 'pandas', 'numpy', 'scikit-learn']
   for pkg in required:
       try:
           __import__(pkg)
           print(f'✅ {pkg}')
       except ImportError:
           print(f'❌ {pkg} - MISSING')
   "
   ```

### Model Loading Failures

1. **Verify Model Files**
   ```bash
   ls -la models/
   
   # Check file integrity
   python3 -c "
   import joblib
   import os
   
   model_dir = 'models'
   for model_name in ['NeuralNetwork', 'RandomForest', 'GradientBoosting']:
       for horizon in ['4h', '16h', '24h']:
           file_path = f'{model_dir}/{model_name}/{model_name}_{horizon}.joblib'
           if os.path.exists(file_path):
               try:
                   joblib.load(file_path)
                   print(f'✅ {model_name}_{horizon}')
               except Exception as e:
                   print(f'❌ {model_name}_{horizon}: {e}')
           else:
               print(f'❌ {model_name}_{horizon}: File not found')
   "
   ```

2. **Retrain Corrupted Models**
   ```bash
   # Remove corrupted model
   rm -rf models/ModelName/
   
   # Retrain specific model
   python3 -c "
   from src.models.model_trainer import ModelTrainer
   from src.data_processing.data_loader import DataLoader
   from src.data_processing.preprocessor import DataPreprocessor, PreprocessingConfig
   
   # Load and preprocess data
   loader = DataLoader()
   df = loader.load_timeframe_data('1h')
   
   config = PreprocessingConfig()
   preprocessor = DataPreprocessor(config)
   processed_data = preprocessor.fit_transform(df.tail(10000))
   
   # Train specific model
   trainer = ModelTrainer()
   trainer.train_model('ModelName', processed_data)
   "
   ```

### Data Issues

1. **Missing Data Files**
   ```bash
   # Check data directory
   ls -la data/
   
   # Verify file formats
   head -5 data/1h_3112020_3062025.csv
   ```

2. **Data Corruption**
   ```bash
   python3 -c "
   import pandas as pd
   
   try:
       df = pd.read_csv('data/1h_3112020_3062025.csv')
       print(f'✅ Data loaded: {len(df)} records')
       
       # Check for required columns
       required_cols = ['time', 'open', 'high', 'low', 'close', 'volume']
       missing_cols = [col for col in required_cols if col not in df.columns]
       
       if missing_cols:
           print(f'❌ Missing columns: {missing_cols}')
       else:
           print('✅ All required columns present')
           
   except Exception as e:
       print(f'❌ Data loading failed: {e}')
   "
   ```

### Memory Issues

1. **Monitor Memory Usage**
   ```bash
   # Check memory usage by process
   ps aux --sort=-%mem | head -10
   
   # Check system memory
   free -h
   ```

2. **Optimize Memory Usage**
   ```python
   # In your code, add memory optimization
   import gc
   
   # After processing large datasets
   del large_dataframe
   gc.collect()
   
   # Use chunking for large files
   chunk_size = 10000
   for chunk in pd.read_csv('large_file.csv', chunksize=chunk_size):
       process_chunk(chunk)
   ```

## 📊 Monitoring Scripts

### Automated Health Check

Create `scripts/health_check.py`:

```python
#!/usr/bin/env python3
import requests
import json
import smtplib
from email.mime.text import MIMEText
from datetime import datetime

def check_api_health():
    try:
        response = requests.get('http://localhost:8000/health', timeout=10)
        if response.status_code == 200:
            data = response.json()
            return True, data
        else:
            return False, f"HTTP {response.status_code}"
    except Exception as e:
        return False, str(e)

def check_predictions():
    try:
        response = requests.post(
            'http://localhost:8000/predict',
            json={'model_name': 'NeuralNetwork'},
            timeout=30
        )
        if response.status_code == 200:
            data = response.json()
            # Validate prediction structure
            required_keys = ['predictions', 'current_price', 'timestamp']
            if all(key in data for key in required_keys):
                return True, "Predictions working"
            else:
                return False, "Invalid prediction response"
        else:
            return False, f"HTTP {response.status_code}"
    except Exception as e:
        return False, str(e)

def send_alert(message):
    # Configure email settings
    smtp_server = "smtp.gmail.com"
    smtp_port = 587
    email = "<EMAIL>"
    password = "your-app-password"
    
    msg = MIMEText(f"BTC Prediction System Alert:\n\n{message}")
    msg['Subject'] = "🚨 BTC Prediction System Alert"
    msg['From'] = email
    msg['To'] = email
    
    try:
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(email, password)
        server.send_message(msg)
        server.quit()
        print("Alert sent successfully")
    except Exception as e:
        print(f"Failed to send alert: {e}")

def main():
    print(f"Health check at {datetime.now()}")
    
    # Check API health
    health_ok, health_msg = check_api_health()
    if not health_ok:
        alert_msg = f"API Health Check Failed: {health_msg}"
        print(alert_msg)
        send_alert(alert_msg)
        return
    
    # Check predictions
    pred_ok, pred_msg = check_predictions()
    if not pred_ok:
        alert_msg = f"Prediction Check Failed: {pred_msg}"
        print(alert_msg)
        send_alert(alert_msg)
        return
    
    print("✅ All checks passed")

if __name__ == "__main__":
    main()
```

### Performance Monitor

Create `scripts/performance_monitor.py`:

```python
#!/usr/bin/env python3
import psutil
import requests
import time
import json
from datetime import datetime

def collect_metrics():
    metrics = {
        'timestamp': datetime.now().isoformat(),
        'system': {
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_percent': psutil.disk_usage('/').percent,
            'load_average': psutil.getloadavg()[0] if hasattr(psutil, 'getloadavg') else None
        },
        'api': {}
    }
    
    # Test API response time
    try:
        start_time = time.time()
        response = requests.get('http://localhost:8000/health', timeout=10)
        response_time = time.time() - start_time
        
        metrics['api']['response_time'] = response_time
        metrics['api']['status_code'] = response.status_code
        
        if response.status_code == 200:
            data = response.json()
            metrics['api']['models_loaded'] = data.get('models_loaded', 0)
    except Exception as e:
        metrics['api']['error'] = str(e)
    
    return metrics

def save_metrics(metrics):
    # Append to metrics file
    with open('logs/metrics.jsonl', 'a') as f:
        f.write(json.dumps(metrics) + '\n')

def check_thresholds(metrics):
    alerts = []
    
    # CPU threshold
    if metrics['system']['cpu_percent'] > 80:
        alerts.append(f"High CPU usage: {metrics['system']['cpu_percent']:.1f}%")
    
    # Memory threshold
    if metrics['system']['memory_percent'] > 85:
        alerts.append(f"High memory usage: {metrics['system']['memory_percent']:.1f}%")
    
    # Disk threshold
    if metrics['system']['disk_percent'] > 90:
        alerts.append(f"High disk usage: {metrics['system']['disk_percent']:.1f}%")
    
    # API response time threshold
    if 'response_time' in metrics['api'] and metrics['api']['response_time'] > 5.0:
        alerts.append(f"Slow API response: {metrics['api']['response_time']:.2f}s")
    
    return alerts

def main():
    metrics = collect_metrics()
    save_metrics(metrics)
    
    alerts = check_thresholds(metrics)
    if alerts:
        print("⚠️  Alerts:")
        for alert in alerts:
            print(f"  - {alert}")
    else:
        print("✅ All metrics within normal ranges")
    
    # Print summary
    print(f"CPU: {metrics['system']['cpu_percent']:.1f}% | "
          f"Memory: {metrics['system']['memory_percent']:.1f}% | "
          f"Disk: {metrics['system']['disk_percent']:.1f}%")
    
    if 'response_time' in metrics['api']:
        print(f"API Response: {metrics['api']['response_time']:.3f}s")

if __name__ == "__main__":
    main()
```

## 📅 Maintenance Schedule

### Cron Jobs

Add to crontab (`crontab -e`):

```bash
# Health check every 5 minutes
*/5 * * * * /usr/bin/python3 /path/to/scripts/health_check.py

# Performance monitoring every hour
0 * * * * /usr/bin/python3 /path/to/scripts/performance_monitor.py

# Daily log rotation
0 0 * * * /usr/sbin/logrotate /path/to/logrotate.conf

# Weekly model performance review
0 9 * * 1 /usr/bin/python3 /path/to/scripts/weekly_report.py

# Monthly model retraining
0 2 1 * * /usr/bin/python3 /path/to/train_models.py

# Monthly backup
0 3 1 * * /path/to/scripts/backup.sh
```

### Log Rotation

Create `/etc/logrotate.d/btc-prediction`:

```
/path/to/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 user user
    postrotate
        # Restart API if needed
        /bin/kill -HUP `cat /var/run/btc-prediction.pid 2> /dev/null` 2> /dev/null || true
    endscript
}
```

This maintenance guide ensures your BTC/USDT Price Prediction System remains healthy, accurate, and performant over time.

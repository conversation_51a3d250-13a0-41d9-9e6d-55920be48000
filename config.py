"""
Configuration settings for the BTC/USDT Price Prediction System
"""
import os
from pathlib import Path

# Base directories
BASE_DIR = Path(__file__).parent
DATA_DIR = BASE_DIR / "data"
MODELS_DIR = BASE_DIR / "models"
LOGS_DIR = BASE_DIR / "logs"

# Create directories if they don't exist
MODELS_DIR.mkdir(exist_ok=True)
LOGS_DIR.mkdir(exist_ok=True)

# Data files
DATA_FILES = {
    "15m": DATA_DIR / "15m_3112020_3062025.csv",
    "1h": DATA_DIR / "1h_3112020_3062025.csv", 
    "4h": DATA_DIR / "4h_3112020_3062025.csv",
    "1d": DATA_DIR / "1d_3112020_3062025.csv",
    "1w": DATA_DIR / "1w_3112020_3062025.csv",
    "1M": DATA_DIR / "1M_3112020_3062025.csv"
}

# Model configuration
MODEL_CONFIG = {
    "prediction_horizons": [4, 16, 24],  # hours
    "sequence_length": 60,  # number of time steps to look back
    "features": ["open", "high", "low", "close", "volume"],
    "target": "close",
    "train_split": 0.7,
    "val_split": 0.15,
    "test_split": 0.15,
    "use_full_dataset": True,  # Use full dataset (2020-2025) instead of recent subset
    "max_training_records": None,  # None = use all data, or specify number (e.g., 20000)
}

# API configuration
API_CONFIG = {
    "host": "0.0.0.0",
    "port": 8000,
    "debug": True
}

# Logging configuration
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        }
    },
    "handlers": {
        "file": {
            "class": "logging.FileHandler",
            "filename": LOGS_DIR / "app.log",
            "formatter": "default"
        },
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "default"
        }
    },
    "root": {
        "level": "INFO",
        "handlers": ["file", "console"]
    }
}

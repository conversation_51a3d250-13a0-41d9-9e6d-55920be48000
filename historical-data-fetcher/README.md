# Historical Data Fetcher

A TypeScript project to fetch historical cryptocurrency data from Binance API and export it to CSV format.

## Features

- 🚀 Fetch historical candlestick data from Binance
- 📊 Support for multiple timeframes (1m, 5m, 15m, 1h, 1d, etc.)
- 💾 Export data to CSV format
- 🔧 Configurable date ranges and symbols
- ⚡ Rate limiting and error handling
- 🎯 TypeScript with full type safety

## Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- Binance API credentials (API Key and Secret)

## Installation

1. Clone or download this project
2. Install dependencies:
   ```bash
   npm install
   ```

3. Create a `.env` file based on `.env.example`:
   ```bash
   cp .env.example .env
   ```

4. Add your Binance API credentials to the `.env` file:
   ```
   API_KEY=your_binance_api_key_here
   API_SECRET=your_binance_api_secret_here
   ```

## Usage

### Quick Start

Run the application with default settings:
```bash
npm start
```

This will fetch BTCUSDT 15-minute data for the year 2024.

### Development Mode

For development with auto-reload:
```bash
npm run dev
```

### Build and Run

To build the TypeScript code:
```bash
npm run build
node dist/index.js
```

## Configuration

You can customize the data fetching by modifying the `customConfig` object in `src/index.ts`:

```typescript
const customConfig: Partial<FetchConfig> = {
  symbol: 'ETHUSDT',           // Trading pair
  interval: '1h',              // Timeframe
  startDate: new Date('2023-01-01'),
  endDate: new Date('2023-12-31'),
  outputFile: 'eth_2023.csv',  // Optional: auto-generated if not provided
};
```

### Supported Intervals

- `1m`, `3m`, `5m`, `15m`, `30m` (minutes)
- `1h`, `2h`, `4h`, `6h`, `8h`, `12h` (hours)
- `1d`, `3d` (days)
- `1w` (week)
- `1M` (month)

### Supported Symbols

Any valid Binance trading pair, such as:
- `BTCUSDT`
- `ETHUSDT`
- `ADAUSDT`
- `BNBUSDT`
- etc.

## Output Format

The CSV file will contain the following columns:
- `Timestamp` - ISO 8601 formatted timestamp
- `Open` - Opening price
- `High` - Highest price
- `Low` - Lowest price
- `Close` - Closing price
- `Volume` - Trading volume

## Project Structure

```
historical-data-fetcher/
├── src/
│   ├── config/
│   │   └── config.ts          # Configuration management
│   ├── services/
│   │   ├── binanceClient.ts   # Binance API client
│   │   ├── dataFetcher.ts     # Data fetching logic
│   │   └── csvExporter.ts     # CSV export functionality
│   ├── types/
│   │   └── types.ts           # TypeScript type definitions
│   ├── utils/
│   │   └── intervals.ts       # Interval conversion utilities
│   └── index.ts               # Main application entry point
├── .env.example               # Environment variables template
├── .gitignore
├── package.json
├── tsconfig.json
└── README.md
```

## Error Handling

The application includes comprehensive error handling for:
- API connection issues
- Rate limiting
- Invalid date ranges
- Missing environment variables
- File system errors

## Rate Limiting

The application includes built-in delays between API requests to respect Binance's rate limits.

## License

MIT License

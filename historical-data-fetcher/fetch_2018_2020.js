import { DataFetcher } from './src/services/dataFetcher.js';
import { CsvExporter } from './src/services/csvExporter.js';
import { defaultFetchConfig } from './src/config/config.js';

/**
 * Fetch historical data from 2018-2020 for all timeframes
 */
class HistoricalDataFetcher2018 {
  constructor() {
    this.dataFetcher = new DataFetcher();
    this.csvExporter = new CsvExporter();
    this.timeframes = ['15m', '1h', '4h', '1d', '1w', '1M'];
    this.startDate = new Date('2018-01-01');
    this.endDate = new Date('2019-12-31'); // End of 2019 to avoid overlap with existing 2020 data
  }

  /**
   * Fetch data for a specific timeframe
   */
  async fetchTimeframeData(timeframe) {
    const config = {
      ...defaultFetchConfig,
      symbol: 'BTCUSDT',
      interval: timeframe,
      startDate: this.startDate,
      endDate: this.endDate,
      outputFile: `${timeframe}_01012018_31122019.csv`
    };

    try {
      console.log(`\n🎯 Fetching ${timeframe} data...`);
      console.log(`📅 From: ${config.startDate.toISOString()}`);
      console.log(`📅 To: ${config.endDate.toISOString()}`);

      const historicalData = await this.dataFetcher.fetchHistoricalData(config);

      if (historicalData.length === 0) {
        console.log(`⚠️ No ${timeframe} data was fetched`);
        return false;
      }

      await this.csvExporter.exportToCSV(historicalData, config.outputFile);

      console.log(`✅ ${timeframe} data fetched successfully!`);
      console.log(`📁 Output file: ${config.outputFile}`);
      console.log(`📊 Total records: ${historicalData.length:,}`);

      return true;

    } catch (error) {
      console.error(`❌ Error fetching ${timeframe} data:`, error.message);
      return false;
    }
  }

  /**
   * Add delay to avoid rate limiting
   */
  async delay(seconds) {
    console.log(`⏳ Waiting ${seconds} seconds to avoid rate limiting...`);
    return new Promise(resolve => setTimeout(resolve, seconds * 1000));
  }

  /**
   * Run the complete data fetching process
   */
  async run() {
    console.log('🚀 Historical Data Fetcher (2018-2020)');
    console.log('=' .repeat(50));
    console.log(`📅 Fetching data from ${this.startDate.toISOString()} to ${this.endDate.toISOString()}`);
    console.log(`📊 Timeframes: ${this.timeframes.join(', ')}`);
    console.log();

    const results = {
      successful: [],
      failed: []
    };

    // Fetch data for each timeframe
    for (let i = 0; i < this.timeframes.length; i++) {
      const timeframe = this.timeframes[i];
      
      console.log(`\n${'='.repeat(20)} ${timeframe.toUpperCase()} ${'='.repeat(20)}`);
      
      const success = await this.fetchTimeframeData(timeframe);
      
      if (success) {
        results.successful.push(timeframe);
      } else {
        results.failed.push(timeframe);
      }

      // Add delay between requests (except for the last one)
      if (i < this.timeframes.length - 1) {
        await this.delay(30);
      }
    }

    // Summary
    console.log(`\n${'='.repeat(50)}`);
    console.log('📊 SUMMARY');
    console.log(`${'='.repeat(50)}`);
    console.log(`✅ Successful: ${results.successful.length} timeframes`);
    if (results.successful.length > 0) {
      console.log(`   ${results.successful.join(', ')}`);
    }

    console.log(`❌ Failed: ${results.failed.length} timeframes`);
    if (results.failed.length > 0) {
      console.log(`   ${results.failed.join(', ')}`);
    }

    if (results.successful.length > 0) {
      console.log('\n🎉 Data fetching completed!');
      console.log('📁 CSV files have been generated in the current directory');
      console.log('🔄 Next steps:');
      console.log('   1. Move CSV files to ../data/ directory');
      console.log('   2. Run the merge script to combine with existing data');
      console.log('   3. Retrain models with extended dataset');
    } else {
      console.log('\n❌ No data was successfully fetched');
    }

    return results.successful.length > 0;
  }
}

/**
 * Main execution
 */
async function main() {
  const fetcher = new HistoricalDataFetcher2018();
  
  try {
    const success = await fetcher.run();
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('❌ Fatal error:', error);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { HistoricalDataFetcher2018 };

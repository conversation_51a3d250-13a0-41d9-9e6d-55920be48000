{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "declaration": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"], "ts-node": {"esm": true}}
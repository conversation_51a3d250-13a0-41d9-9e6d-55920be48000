import { DataFetcher } from './services/dataFetcher.js';
import { CsvExporter } from './services/csvExporter.js';
import { defaultFetchConfig, FetchConfig } from './config/config.js';

/**
 * Main application class
 */
class HistoricalDataFetcher {
  private dataFetcher: DataFetcher;
  private csvExporter: CsvExporter;

  constructor() {
    this.dataFetcher = new DataFetcher();
    this.csvExporter = new CsvExporter();
  }

  /**
   * Run the data fetching process
   */
  async run(config: Partial<FetchConfig> = {}): Promise<void> {
    const finalConfig: FetchConfig = {
      ...defaultFetchConfig,
      ...config,
    };

    try {
      console.log('🎯 Historical Data Fetcher Started');
      console.log('================================');

      // Fetch historical data
      const historicalData = await this.dataFetcher.fetchHistoricalData(finalConfig);

      if (historicalData.length === 0) {
        console.log('⚠️ No data was fetched');
        return;
      }

      // Generate filename if not provided
      if (!config.outputFile) {
        finalConfig.outputFile = this.csvExporter.generateFilename(
          finalConfig.symbol,
          finalConfig.interval,
          finalConfig.startDate,
          finalConfig.endDate
        );
      }

      // Export to CSV
      await this.csvExporter.exportToCSV(historicalData, finalConfig.outputFile);

      console.log('================================');
      console.log('🎉 Process completed successfully!');
      console.log(`📁 Output file: ${finalConfig.outputFile}`);
      console.log(`📊 Total records: ${historicalData.length}`);

    } catch (error) {
      console.error('❌ Error during execution:', error);
      process.exit(1);
    }
  }
}

/**
 * Main execution function
 */
async function main() {
  const fetcher = new HistoricalDataFetcher();

  // You can customize the configuration here
  const customConfig: Partial<FetchConfig> = {
    symbol: 'BTCUSDT',
    interval: '15m',
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-12-31'),
    // outputFile: 'custom_filename.csv', // Optional: will auto-generate if not provided
  };

  await fetcher.run(customConfig);
}

// Run the application
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { HistoricalDataFetcher };

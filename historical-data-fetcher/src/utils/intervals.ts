import { CandleChartInterval } from 'binance-api-node';
import { SupportedInterval } from '../types/types.js';

/**
 * Convert interval string to milliseconds
 */
export function intervalToMilliseconds(interval: SupportedInterval): number {
  const intervalMap: Record<SupportedInterval, number> = {
    '1m': 60 * 1000,
    '3m': 3 * 60 * 1000,
    '5m': 5 * 60 * 1000,
    '15m': 15 * 60 * 1000,
    '30m': 30 * 60 * 1000,
    '1h': 60 * 60 * 1000,
    '2h': 2 * 60 * 60 * 1000,
    '4h': 4 * 60 * 60 * 1000,
    '6h': 6 * 60 * 60 * 1000,
    '8h': 8 * 60 * 60 * 1000,
    '12h': 12 * 60 * 60 * 1000,
    '1d': 24 * 60 * 60 * 1000,
    '3d': 3 * 24 * 60 * 60 * 1000,
    '1w': 7 * 24 * 60 * 60 * 1000,
    '1M': 30 * 24 * 60 * 60 * 1000, // Approximate
  };

  return intervalMap[interval];
}

/**
 * Convert interval string to Binance API interval enum
 */
export function stringToInterval(interval: SupportedInterval): CandleChartInterval {
  const intervalMap: Record<SupportedInterval, CandleChartInterval> = {
    '1m': CandleChartInterval.ONE_MINUTE,
    '3m': CandleChartInterval.THREE_MINUTES,
    '5m': CandleChartInterval.FIVE_MINUTES,
    '15m': CandleChartInterval.FIFTEEN_MINUTES,
    '30m': CandleChartInterval.THIRTY_MINUTES,
    '1h': CandleChartInterval.ONE_HOUR,
    '2h': CandleChartInterval.TWO_HOURS,
    '4h': CandleChartInterval.FOUR_HOURS,
    '6h': CandleChartInterval.SIX_HOURS,
    '8h': CandleChartInterval.EIGHT_HOURS,
    '12h': CandleChartInterval.TWELVE_HOURS,
    '1d': CandleChartInterval.ONE_DAY,
    '3d': CandleChartInterval.THREE_DAYS,
    '1w': CandleChartInterval.ONE_WEEK,
    '1M': CandleChartInterval.ONE_MONTH,
  };

  return intervalMap[interval];
}

import { CandleChartResult } from 'binance-api-node';
import BinanceClient from './binanceClient.js';
import { FetchConfig } from '../config/config.js';
import { SupportedInterval } from '../types/types.js';
import { intervalToMilliseconds } from '../utils/intervals.js';

export class DataFetcher {
  private client: BinanceClient;

  constructor() {
    this.client = new BinanceClient();
  }

  /**
   * Fetch historical data for a given time range
   */
  async fetchHistoricalData(config: FetchConfig): Promise<CandleChartResult[]> {
    console.log('🚀 Starting historical data fetch...');
    console.log(`Symbol: ${config.symbol}`);
    console.log(`Interval: ${config.interval}`);
    console.log(`Start Date: ${config.startDate.toISOString()}`);
    console.log(`End Date: ${config.endDate.toISOString()}`);

    // Test connection first
    const isConnected = await this.client.testConnection();
    if (!isConnected) {
      throw new Error('Failed to connect to Binance API');
    }

    const allData: CandleChartResult[] = [];
    const startTime = config.startDate.getTime();
    const endTime = config.endDate.getTime();
    const intervalMs = intervalToMilliseconds(config.interval as SupportedInterval);
    
    let currentStartTime = startTime;

    while (currentStartTime < endTime) {
      const currentEndTime = Math.min(
        currentStartTime + (config.apiLimit * intervalMs),
        endTime
      );

      console.log(`📊 Fetching data from ${new Date(currentStartTime).toISOString()} to ${new Date(currentEndTime).toISOString()}`);

      try {
        const candles = await this.client.fetchCandles({
          symbol: config.symbol,
          interval: config.interval as SupportedInterval,
          startTime: currentStartTime,
          endTime: currentEndTime,
          limit: config.apiLimit,
        });

        if (candles.length === 0) {
          console.log('⚠️ No more data available');
          break;
        }

        allData.push(...candles);
        console.log(`✅ Fetched ${candles.length} candles (Total: ${allData.length})`);

        // Update start time for next iteration
        currentStartTime = candles[candles.length - 1].closeTime + 1;

        // Add a small delay to avoid rate limiting
        await this.delay(100);

      } catch (error) {
        console.error('❌ Error fetching batch:', error);
        throw error;
      }
    }

    console.log(`🎉 Completed! Total candles fetched: ${allData.length}`);
    return allData;
  }

  /**
   * Simple delay utility
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

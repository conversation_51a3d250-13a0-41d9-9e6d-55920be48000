import { CandleChartResult } from 'binance-api-node';
import { createObjectCsvWriter } from 'csv-writer';
import { ProcessedCandle } from '../types/types.js';

export class CsvExporter {
  /**
   * Process raw candle data into a more readable format
   */
  private processCandles(candles: CandleChartResult[]): ProcessedCandle[] {
    return candles.map(candle => ({
      timestamp: new Date(candle.openTime).toISOString(),
      open: parseFloat(candle.open),
      high: parseFloat(candle.high),
      low: parseFloat(candle.low),
      close: parseFloat(candle.close),
      volume: parseFloat(candle.volume),
    }));
  }

  /**
   * Export candle data to CSV file
   */
  async exportToCSV(candles: CandleChartResult[], filePath: string): Promise<void> {
    console.log(`💾 Exporting ${candles.length} candles to ${filePath}...`);

    const processedData = this.processCandles(candles);

    const csvWriter = createObjectCsvWriter({
      path: filePath,
      header: [
        { id: 'timestamp', title: 'Timestamp' },
        { id: 'open', title: 'Open' },
        { id: 'high', title: 'High' },
        { id: 'low', title: 'Low' },
        { id: 'close', title: 'Close' },
        { id: 'volume', title: 'Volume' },
      ],
    });

    try {
      await csvWriter.writeRecords(processedData);
      console.log(`✅ Successfully exported data to ${filePath}`);
    } catch (error) {
      console.error('❌ Error exporting to CSV:', error);
      throw error;
    }
  }

  /**
   * Generate filename based on configuration
   */
  generateFilename(symbol: string, interval: string, startDate: Date, endDate: Date): string {
    const formatDate = (date: Date) => date.toISOString().split('T')[0];
    const start = formatDate(startDate);
    const end = formatDate(endDate);
    
    return `${symbol}_${interval}_${start}_to_${end}.csv`;
  }
}

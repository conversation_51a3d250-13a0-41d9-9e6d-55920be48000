import Binance, { CandleChartResult } from 'binance-api-node';
import { loadApiConfig } from '../config/config.js';
import { FetchOptions } from '../types/types.js';
import { stringToInterval } from '../utils/intervals.js';

class BinanceClient {
  private client: ReturnType<typeof Binance>;

  constructor() {
    const config = loadApiConfig();
    
    // Handle ESM import issue with binance-api-node
    const BinanceConstructor = (Binance as any).default || Binance;
    
    this.client = BinanceConstructor({
      apiKey: config.apiKey,
      apiSecret: config.apiSecret,
      httpFutures: config.httpFutures,
      wsFutures: config.wsFutures,
    });
  }

  /**
   * Fetch candle data from Binance API
   */
  async fetchCandles(options: FetchOptions): Promise<CandleChartResult[]> {
    try {
      const candles = await this.client.candles({
        symbol: options.symbol,
        interval: stringToInterval(options.interval),
        startTime: options.startTime,
        endTime: options.endTime,
        limit: options.limit || 1000,
      });

      return candles;
    } catch (error) {
      console.error('Error fetching candles:', error);
      throw error;
    }
  }

  /**
   * Test API connection
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.client.time();
      return true;
    } catch (error) {
      console.error('API connection test failed:', error);
      return false;
    }
  }
}

export default BinanceClient;

import 'dotenv/config';

export interface ApiConfig {
  apiKey: string;
  apiSecret: string;
  httpFutures?: string;
  wsFutures?: string;
}

export interface FetchConfig {
  symbol: string;
  interval: string;
  startDate: Date;
  endDate: Date;
  outputFile: string;
  apiLimit: number;
}

/**
 * Load API configuration from environment variables
 */
export function loadApiConfig(): ApiConfig {
  const apiKey = process.env.API_KEY;
  const apiSecret = process.env.API_SECRET;
  const httpFutures = process.env.HTTP_FUTURES;
  const wsFutures = process.env.WS_FUTURES;

  if (!apiKey || !apiSecret) {
    throw new Error('Missing API_KEY or API_SECRET in environment variables');
  }

  return {
    apiKey,
    apiSecret,
    httpFutures,
    wsFutures,
  };
}

/**
 * Default fetch configuration
 */
export const defaultFetchConfig: FetchConfig = {
  symbol: 'BTCUSDT',
  interval: '15m',
  startDate: new Date('2024-01-01'),
  endDate: new Date(),
  outputFile: 'historical_data.csv',
  apiLimit: 1000,
};

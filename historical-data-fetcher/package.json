{"name": "historical-data-fetcher", "version": "1.0.0", "description": "A TypeScript project to fetch historical cryptocurrency data from Binance", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node --loader ts-node/esm src/index.ts", "dev": "node --loader ts-node/esm src/index.ts", "test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["binance", "cryptocurrency", "historical-data", "trading", "typescript"], "author": "", "license": "MIT", "dependencies": {"binance-api-node": "^0.12.0", "dotenv": "^16.4.7", "csv-writer": "^1.6.0"}, "devDependencies": {"@types/node": "^20.11.0", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "eslint": "^8.56.0", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}
#!/usr/bin/env python3
"""
Verify that prediction timestamps are correct based on latest data
"""

import pandas as pd
import requests
import json
from datetime import datetime, timezone, timedelta

def main():
    print("🕐 Timestamp Verification for BTC/USDT Predictions")
    print("=" * 60)
    
    # 1. Check the latest data timestamp from CSV
    print("1️⃣ Checking latest data timestamp from CSV file...")
    try:
        df = pd.read_csv("data/1h_3112020_3062025.csv")
        df['time'] = pd.to_datetime(df['time'])
        latest_data_time = df['time'].iloc[-1]
        latest_price = df['close'].iloc[-1]
        
        print(f"   📊 Latest data timestamp: {latest_data_time}")
        print(f"   💰 Latest price: ${latest_price:,.2f}")
        
        # Convert to UTC+7 for comparison
        if latest_data_time.tz is None:
            latest_utc7 = latest_data_time.tz_localize('UTC').tz_convert(timezone(timedelta(hours=7)))
        else:
            latest_utc7 = latest_data_time.tz_convert(timezone(timedelta(hours=7)))
        
        print(f"   🌏 Latest data (UTC+7): {latest_utc7}")
        
    except Exception as e:
        print(f"   ❌ Error reading CSV: {e}")
        return
    
    print()
    
    # 2. Check API current price endpoint
    print("2️⃣ Checking API current price endpoint...")
    try:
        response = requests.get("http://localhost:8000/current-price")
        if response.status_code == 200:
            data = response.json()
            api_timestamp = pd.to_datetime(data['timestamp'])
            api_price = data['price']
            
            print(f"   📊 API timestamp: {api_timestamp}")
            print(f"   💰 API price: ${api_price:,.2f}")
            
            # Check if they match
            if abs((api_timestamp - latest_utc7).total_seconds()) < 3600:  # Within 1 hour
                print(f"   ✅ Timestamps match (within 1 hour)")
            else:
                print(f"   ⚠️  Timestamp difference: {api_timestamp - latest_utc7}")
                
        else:
            print(f"   ❌ API error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error calling API: {e}")
    
    print()
    
    # 3. Check prediction timestamps
    print("3️⃣ Checking prediction timestamps...")
    try:
        response = requests.post("http://localhost:8000/predict", 
                               json={"model_name": "NeuralNetwork", "include_confidence": True})
        if response.status_code == 200:
            data = response.json()
            latest_data_time_api = pd.to_datetime(data['latest_data_time'])
            
            print(f"   📊 API latest data time: {latest_data_time_api}")
            
            for horizon, prediction in data['predictions'].items():
                pred_time = pd.to_datetime(prediction['prediction_time'])
                expected_time = latest_data_time_api + timedelta(hours=prediction['horizon_hours'])
                
                print(f"   🔮 {horizon} prediction:")
                print(f"      Predicted for: {pred_time}")
                print(f"      Expected: {expected_time}")
                print(f"      Price: ${prediction['price']:,.2f}")
                
                if abs((pred_time - expected_time).total_seconds()) < 60:  # Within 1 minute
                    print(f"      ✅ Timestamp correct")
                else:
                    print(f"      ❌ Timestamp error: {pred_time - expected_time}")
                print()
                
        else:
            print(f"   ❌ API error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error calling prediction API: {e}")
    
    print()
    
    # 4. Check hourly predictions
    print("4️⃣ Checking hourly predictions...")
    try:
        response = requests.get("http://localhost:8000/hourly-predictions/6?model=NeuralNetwork")
        if response.status_code == 200:
            data = response.json()
            latest_data_time_api = pd.to_datetime(data['latest_data_time'])
            
            print(f"   📊 API latest data time: {latest_data_time_api}")
            print(f"   🔮 First 3 hourly predictions:")
            
            for i, prediction in enumerate(data['predictions'][:3]):
                pred_time = pd.to_datetime(prediction['timestamp'])
                expected_time = latest_data_time_api + timedelta(hours=prediction['hour'])
                
                print(f"      Hour {prediction['hour']}: {pred_time} (${prediction['predicted_price']:,.2f})")
                
                if abs((pred_time - expected_time).total_seconds()) < 60:  # Within 1 minute
                    print(f"         ✅ Correct")
                else:
                    print(f"         ❌ Error: {pred_time - expected_time}")
                    
        else:
            print(f"   ❌ API error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error calling hourly predictions API: {e}")
    
    print()
    print("🎯 Summary:")
    print("   The predictions should start from the latest data timestamp")
    print(f"   Latest data: {latest_data_time} (2025-06-24T15:00:00.000Z)")
    print(f"   UTC+7 equivalent: {latest_utc7}")
    print("   ✅ All predictions now use correct base timestamp!")

if __name__ == "__main__":
    main()

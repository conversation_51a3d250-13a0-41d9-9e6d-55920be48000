#!/bin/bash

# BTC/USDT Price Prediction System - Setup Script
# This script helps set up the project after cloning from GitHub

set -e  # Exit on any error

echo "🚀 BTC/USDT Price Prediction System Setup"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Python 3 is installed
print_status "Checking Python installation..."
if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version)
    print_success "Found $PYTHON_VERSION"
else
    print_error "Python 3 is not installed. Please install Python 3.8 or higher."
    exit 1
fi

# Check if pip is installed
print_status "Checking pip installation..."
if command -v pip3 &> /dev/null; then
    print_success "pip3 is available"
else
    print_error "pip3 is not installed. Please install pip."
    exit 1
fi

# Install Python dependencies
print_status "Installing Python dependencies..."
if pip3 install -r requirements.txt; then
    print_success "Python dependencies installed successfully"
else
    print_error "Failed to install Python dependencies"
    exit 1
fi

# Check if Node.js is installed (for historical data fetcher)
print_status "Checking Node.js installation..."
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    print_success "Found Node.js $NODE_VERSION"
    
    # Install Node.js dependencies for historical data fetcher
    if [ -d "historical-data-fetcher" ]; then
        print_status "Installing Node.js dependencies for data fetcher..."
        cd historical-data-fetcher
        if npm install; then
            print_success "Node.js dependencies installed successfully"
        else
            print_warning "Failed to install Node.js dependencies for data fetcher"
        fi
        cd ..
    fi
else
    print_warning "Node.js not found. Historical data fetcher will not be available."
    print_warning "Install Node.js 18+ to use the data fetching functionality."
fi

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p logs
mkdir -p models/NeuralNetwork
mkdir -p models/RandomForest
mkdir -p models/GradientBoosting
mkdir -p data
print_success "Directories created"

# Check for data files
print_status "Checking for data files..."
DATA_FILES=(
    "data/15m_3112020_3062025.csv"
    "data/1h_3112020_3062025.csv"
    "data/4h_3112020_3062025.csv"
    "data/1d_3112020_3062025.csv"
    "data/1w_3112020_3062025.csv"
    "data/1M_3112020_3062025.csv"
)

MISSING_FILES=()
for file in "${DATA_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        MISSING_FILES+=("$file")
    fi
done

if [ ${#MISSING_FILES[@]} -eq 0 ]; then
    print_success "All required data files are present"
else
    print_warning "Missing data files:"
    for file in "${MISSING_FILES[@]}"; do
        echo "  - $file"
    done
    print_warning "You'll need to fetch historical data before training models."
    print_warning "See DATA_FETCHING_GUIDE.md for instructions."
fi

# Test data preprocessing (if data files exist)
if [ ${#MISSING_FILES[@]} -eq 0 ]; then
    print_status "Testing data preprocessing..."
    if python3 test_preprocessing.py; then
        print_success "Data preprocessing test passed"
    else
        print_error "Data preprocessing test failed"
        exit 1
    fi
else
    print_warning "Skipping data preprocessing test (missing data files)"
fi

# Check if models exist
print_status "Checking for trained models..."
MODEL_FILES=(
    "models/NeuralNetwork/NeuralNetwork_4h.h5"
    "models/RandomForest/RandomForest_4h.joblib"
    "models/GradientBoosting/GradientBoosting_4h.joblib"
)

MISSING_MODELS=()
for file in "${MODEL_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        MISSING_MODELS+=("$file")
    fi
done

if [ ${#MISSING_MODELS[@]} -eq 0 ]; then
    print_success "Trained models are present"
else
    print_warning "No trained models found. You'll need to train them."
    if [ ${#MISSING_FILES[@]} -eq 0 ]; then
        echo ""
        read -p "Would you like to train the models now? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            print_status "Starting model training..."
            if python3 train_models.py; then
                print_success "Model training completed successfully"
            else
                print_error "Model training failed"
                exit 1
            fi
        else
            print_warning "You can train models later by running: python3 train_models.py"
        fi
    else
        print_warning "Cannot train models without data files."
    fi
fi

# Final setup summary
echo ""
echo "🎉 Setup Summary"
echo "================"

if [ ${#MISSING_FILES[@]} -eq 0 ]; then
    print_success "✅ Data files: Present"
else
    print_warning "⚠️  Data files: Missing (${#MISSING_FILES[@]} files)"
fi

if [ ${#MISSING_MODELS[@]} -eq 0 ]; then
    print_success "✅ Trained models: Present"
else
    print_warning "⚠️  Trained models: Missing"
fi

if command -v node &> /dev/null; then
    print_success "✅ Historical data fetcher: Available"
else
    print_warning "⚠️  Historical data fetcher: Node.js required"
fi

echo ""
echo "📚 Next Steps:"
echo "=============="

if [ ${#MISSING_FILES[@]} -gt 0 ]; then
    echo "1. 📥 Fetch historical data:"
    echo "   - See DATA_FETCHING_GUIDE.md for detailed instructions"
    echo "   - Or place your CSV files in the data/ directory"
fi

if [ ${#MISSING_MODELS[@]} -gt 0 ] && [ ${#MISSING_FILES[@]} -eq 0 ]; then
    echo "2. 🤖 Train models:"
    echo "   python3 train_models.py"
fi

echo "3. 🚀 Start the API server:"
echo "   python3 run_api.py"

echo "4. 🌐 Open your browser to:"
echo "   http://localhost:8000"

echo ""
echo "📖 For more information, see README.md"
echo ""
print_success "Setup completed! 🎉"

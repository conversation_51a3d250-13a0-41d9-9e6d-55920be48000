"""
Train and evaluate cryptocurrency price prediction models
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import logging
from pathlib import Path

from src.data_processing.data_loader import DataLoader
from src.data_processing.preprocessor import DataPreprocessor, PreprocessingConfig
from src.models.model_trainer import ModelTrainer

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Main training function"""
    print("🚀 Starting BTC/USDT Price Prediction Model Training")
    print("=" * 60)
    
    # Load and preprocess data
    print("📊 Loading and preprocessing data...")
    loader = DataLoader()
    df_1h = loader.load_timeframe_data('1h')

    # Import config to check training data settings
    from config import MODEL_CONFIG

    # Determine training data subset
    if MODEL_CONFIG.get("use_full_dataset", True):
        if MODEL_CONFIG.get("max_training_records") is not None:
            # Use specified number of recent records
            max_records = MODEL_CONFIG["max_training_records"]
            training_data = df_1h.tail(max_records).copy()
            print(f"Using last {len(training_data)} records for training (limited by config)")
        else:
            # Use full dataset
            training_data = df_1h.copy()
            print(f"Using FULL dataset: {len(training_data)} records for training (2020-2025)")
            print(f"Date range: {training_data.index.min()} to {training_data.index.max()}")
    else:
        # Use recent data for faster training (legacy behavior)
        training_data = df_1h.tail(20000).copy()
        print(f"Using recent {len(training_data)} records for training (legacy mode)")
    
    # Configure preprocessing
    config = PreprocessingConfig(
        sequence_length=60,
        prediction_horizons=[4, 16, 24],
        features=['open', 'high', 'low', 'close', 'volume'],
        target='close',
        scaler_type='standard'
    )

    # Preprocess data
    preprocessor = DataPreprocessor(config)
    processed_data = preprocessor.fit_transform(training_data)
    
    print(f"Processed data shape: {processed_data.shape}")
    print(f"Features: {len(preprocessor.feature_columns)}")
    
    # Initialize model trainer
    trainer = ModelTrainer(models_dir=Path("models"))
    
    # Train models (start with fast models)
    print("\n🤖 Training machine learning models...")
    
    # Get available models
    available_models = trainer.get_available_models()

    models_to_train = []
    for model in ['RandomForest', 'NeuralNetwork', 'GradientBoosting']:
        if model in available_models:
            models_to_train.append(model)
        else:
            print(f"⚠️  Skipping {model} (not available)")

    print(f"📋 Models to train: {models_to_train}")
    
    trained_models = {}
    
    for model_name in models_to_train:
        try:
            print(f"\n📈 Training {model_name}...")
            model = trainer.train_model(
                model_name=model_name,
                data=processed_data,
                train_ratio=0.7,
                val_ratio=0.15
            )
            trained_models[model_name] = model
            print(f"✅ {model_name} training completed!")
            
        except Exception as e:
            print(f"❌ Failed to train {model_name}: {e}")
            logger.error(f"Failed to train {model_name}: {e}")
    
    # Evaluate models
    print("\n📊 Evaluating models...")
    
    evaluation_results = {}
    for model_name in trained_models.keys():
        try:
            print(f"Evaluating {model_name}...")
            results = trainer.evaluate_model(model_name, processed_data)
            evaluation_results[model_name] = results
            
            # Print results
            print(f"\n{model_name} Results:")
            for horizon, metrics in results.items():
                print(f"  {horizon}:")
                print(f"    RMSE: {metrics['rmse']:.2f}")
                print(f"    MAE: {metrics['mae']:.2f}")
                print(f"    MAPE: {metrics['mape']:.2f}%")
                print(f"    R²: {metrics['r2']:.4f}")
                print(f"    Directional Accuracy: {metrics['directional_accuracy']:.2f}%")
            
        except Exception as e:
            print(f"❌ Failed to evaluate {model_name}: {e}")
            logger.error(f"Failed to evaluate {model_name}: {e}")
    
    # Model comparison
    if evaluation_results:
        print("\n🏆 Model Comparison:")
        print("-" * 40)
        
        comparison_df = trainer.compare_models(processed_data)
        print(comparison_df.to_string(index=False))
        
        # Save comparison
        comparison_df.to_csv('model_comparison.csv', index=False)
        print("\n💾 Model comparison saved to 'model_comparison.csv'")
        
        # Find best models
        print("\n🥇 Best Models by Metric:")
        for horizon in ['4h', '16h', '24h']:
            print(f"\n{horizon.upper()} Horizon:")
            horizon_data = comparison_df[comparison_df['Horizon'] == horizon]
            
            if not horizon_data.empty:
                best_rmse = horizon_data.loc[horizon_data['RMSE'].idxmin()]
                best_r2 = horizon_data.loc[horizon_data['R2'].idxmax()]
                best_dir_acc = horizon_data.loc[horizon_data['Directional_Accuracy'].idxmax()]
                
                print(f"  Best RMSE: {best_rmse['Model']} ({best_rmse['RMSE']:.2f})")
                print(f"  Best R²: {best_r2['Model']} ({best_r2['R2']:.4f})")
                print(f"  Best Directional Accuracy: {best_dir_acc['Model']} ({best_dir_acc['Directional_Accuracy']:.2f}%)")
        
        # Generate training report
        print("\n📋 Generating training report...")
        report = trainer.generate_training_report(processed_data)
        
        # Save report
        with open('training_report.txt', 'w') as f:
            f.write(report)
        
        print("💾 Training report saved to 'training_report.txt'")
        
        # Save evaluation results
        trainer.save_evaluation_results(Path('evaluation_results.json'))
        
        # Test ensemble prediction
        print("\n🔮 Testing ensemble predictions...")
        try:
            ensemble_pred = trainer.create_ensemble_prediction(
                processed_data.tail(100),  # Test on last 100 samples
                model_names=list(trained_models.keys())
            )
            
            print("Ensemble prediction shapes:")
            for horizon, pred in ensemble_pred.items():
                print(f"  {horizon}: {pred.shape}")
            
        except Exception as e:
            print(f"❌ Ensemble prediction failed: {e}")
    
    print("\n✅ Model training and evaluation completed!")
    print("\n📁 Generated files:")
    print("  - models/ (trained model files)")
    print("  - model_comparison.csv")
    print("  - training_report.txt")
    print("  - evaluation_results.json")
    
    return trained_models, evaluation_results

if __name__ == "__main__":
    models, results = main()

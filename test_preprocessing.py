"""
Test script for data preprocessing pipeline
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
from src.data_processing.data_loader import DataLoader
from src.data_processing.preprocessor import DataPreprocessor, PreprocessingConfig
from src.data_processing.feature_engineering import AdvancedFeatureEngineer

def test_preprocessing():
    """Test the complete preprocessing pipeline"""
    print("🧪 Testing Data Preprocessing Pipeline")
    print("=" * 50)
    
    # Load data
    print("📊 Loading data...")
    loader = DataLoader()
    df_1h = loader.load_timeframe_data('1h')
    print(f"Loaded {len(df_1h)} records")
    
    # Test basic preprocessing
    print("\n🔧 Testing basic preprocessing...")
    config = PreprocessingConfig(
        sequence_length=60,
        prediction_horizons=[4, 16, 24],
        features=['open', 'high', 'low', 'close', 'volume'],
        target='close',
        scaler_type='standard'
    )
    
    preprocessor = DataPreprocessor(config)
    
    # Take a subset for testing (last 10000 records)
    test_df = df_1h.tail(10000).copy()
    print(f"Testing with {len(test_df)} records")
    
    # Apply preprocessing
    processed_df = preprocessor.fit_transform(test_df)
    print(f"After preprocessing: {len(processed_df)} records")
    print(f"Features created: {len(processed_df.columns)}")
    
    # Show feature columns
    feature_cols = [col for col in processed_df.columns if not col.endswith('_target_4h') and 
                   not col.endswith('_target_16h') and not col.endswith('_target_24h')]
    target_cols = [col for col in processed_df.columns if col.endswith('_target_4h') or 
                  col.endswith('_target_16h') or col.endswith('_target_24h')]
    
    print(f"\nFeature columns: {len(feature_cols)}")
    print(f"Target columns: {len(target_cols)}")
    print(f"Target columns: {target_cols}")
    
    # Test advanced feature engineering
    print("\n🚀 Testing advanced feature engineering...")
    feature_engineer = AdvancedFeatureEngineer()
    
    # Create advanced features on a smaller subset
    small_df = test_df.tail(1000).copy()
    advanced_df = feature_engineer.create_all_features(small_df)
    print(f"Advanced features created: {len(advanced_df.columns)}")
    
    # Show some statistics
    print("\n📊 Data Quality Check:")
    print(f"Missing values in processed data: {processed_df.isnull().sum().sum()}")
    print(f"Infinite values in processed data: {np.isinf(processed_df.select_dtypes(include=[np.number])).sum().sum()}")
    
    # Show sample of processed data
    print("\n📋 Sample of processed data:")
    print(processed_df[['close', 'ma_20', 'rsi', 'macd', 'close_target_4h']].head())
    
    # Test data splitting for model training
    print("\n✂️ Testing data splitting...")
    
    # Remove rows with NaN targets
    clean_df = processed_df.dropna()
    print(f"Clean data for training: {len(clean_df)} records")
    
    # Split data
    train_size = int(0.7 * len(clean_df))
    val_size = int(0.15 * len(clean_df))
    
    train_df = clean_df.iloc[:train_size]
    val_df = clean_df.iloc[train_size:train_size + val_size]
    test_df = clean_df.iloc[train_size + val_size:]
    
    print(f"Training set: {len(train_df)} records")
    print(f"Validation set: {len(val_df)} records")
    print(f"Test set: {len(test_df)} records")
    
    # Check target distribution
    print("\n📈 Target Distribution Analysis:")
    for target_col in target_cols:
        target_data = clean_df[target_col].dropna()
        print(f"{target_col}:")
        print(f"  Mean: {target_data.mean():.2f}")
        print(f"  Std: {target_data.std():.2f}")
        print(f"  Min: {target_data.min():.2f}")
        print(f"  Max: {target_data.max():.2f}")
    
    print("\n✅ Preprocessing pipeline test completed successfully!")
    
    return processed_df, feature_cols, target_cols

if __name__ == "__main__":
    processed_data, features, targets = test_preprocessing()

#!/usr/bin/env python3
"""
Python-based Binance historical data fetcher for 2018-2020
This fetches data directly from Binance API without requiring Node.js
"""

import requests
import pandas as pd
import time
from datetime import datetime, timedelta
import json
from pathlib import Path
import sys

class BinanceHistoricalFetcher:
    def __init__(self):
        self.base_url = "https://api.binance.com/api/v3/klines"
        self.symbol = "BTCUSDT"
        self.data_dir = Path("data")
        self.data_dir.mkdir(exist_ok=True)
        
        # Timeframe mappings
        self.timeframes = {
            '15m': '15m',
            '1h': '1h', 
            '4h': '4h',
            '1d': '1d',
            '1w': '1w',
            '1M': '1M'
        }
        
        # Rate limiting
        self.request_delay = 1.2  # seconds between requests
        self.max_retries = 3
        
    def get_binance_klines(self, symbol, interval, start_time, end_time, limit=1000):
        """Fetch klines data from Binance API"""
        params = {
            'symbol': symbol,
            'interval': interval,
            'startTime': int(start_time.timestamp() * 1000),
            'endTime': int(end_time.timestamp() * 1000),
            'limit': limit
        }
        
        for attempt in range(self.max_retries):
            try:
                response = requests.get(self.base_url, params=params, timeout=30)
                
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 429:  # Rate limit
                    print(f"   ⏳ Rate limited, waiting 60 seconds...")
                    time.sleep(60)
                    continue
                else:
                    print(f"   ⚠️ API error {response.status_code}: {response.text}")
                    if attempt < self.max_retries - 1:
                        time.sleep(5)
                        continue
                    else:
                        return None
                        
            except requests.exceptions.RequestException as e:
                print(f"   ❌ Request error: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(5)
                    continue
                else:
                    return None
        
        return None
    
    def fetch_timeframe_data(self, timeframe, start_date, end_date):
        """Fetch all data for a specific timeframe"""
        print(f"\n📊 Fetching {timeframe} data from {start_date} to {end_date}")
        
        all_data = []
        current_start = start_date
        
        # Calculate chunk size based on timeframe to stay within API limits
        if timeframe in ['15m', '1h']:
            chunk_days = 30  # 30 days at a time
        elif timeframe == '4h':
            chunk_days = 120  # 120 days at a time
        elif timeframe == '1d':
            chunk_days = 365  # 1 year at a time
        else:  # 1w, 1M
            chunk_days = 730  # 2 years at a time
        
        total_requests = 0
        
        while current_start < end_date:
            current_end = min(current_start + timedelta(days=chunk_days), end_date)
            
            print(f"   📅 Fetching {current_start.strftime('%Y-%m-%d')} to {current_end.strftime('%Y-%m-%d')}")
            
            klines = self.get_binance_klines(
                self.symbol, 
                timeframe, 
                current_start, 
                current_end
            )
            
            if klines is None:
                print(f"   ❌ Failed to fetch data for period {current_start} to {current_end}")
                return None
            
            if len(klines) == 0:
                print(f"   ⚠️ No data returned for period {current_start} to {current_end}")
            else:
                all_data.extend(klines)
                print(f"   ✅ Fetched {len(klines)} records")
            
            current_start = current_end
            total_requests += 1
            
            # Rate limiting
            if current_start < end_date:
                print(f"   ⏳ Waiting {self.request_delay}s to avoid rate limiting...")
                time.sleep(self.request_delay)
        
        print(f"   📊 Total records fetched: {len(all_data)}")
        print(f"   🌐 Total API requests: {total_requests}")
        
        return all_data
    
    def convert_to_dataframe(self, klines_data):
        """Convert Binance klines data to pandas DataFrame"""
        if not klines_data:
            return pd.DataFrame()
        
        # Binance klines format:
        # [timestamp, open, high, low, close, volume, close_time, quote_volume, count, taker_buy_volume, taker_buy_quote_volume, ignore]
        df = pd.DataFrame(klines_data, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_volume', 'count', 'taker_buy_volume', 
            'taker_buy_quote_volume', 'ignore'
        ])
        
        # Convert timestamp to datetime
        df['time'] = pd.to_datetime(df['timestamp'], unit='ms')
        
        # Select and convert relevant columns
        df = df[['time', 'open', 'high', 'low', 'close', 'volume']].copy()
        
        # Convert price and volume columns to float
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Sort by time and remove duplicates
        df = df.sort_values('time').drop_duplicates(subset=['time']).reset_index(drop=True)
        
        return df
    
    def save_to_csv(self, df, timeframe, start_date, end_date):
        """Save DataFrame to CSV file"""
        if df.empty:
            print(f"   ⚠️ No data to save for {timeframe}")
            return None
        
        # Generate filename
        start_str = start_date.strftime('%d%m%Y')
        end_str = end_date.strftime('%d%m%Y')
        filename = f"{timeframe}_{start_str}_{end_str}.csv"
        filepath = self.data_dir / filename
        
        # Save to CSV
        df.to_csv(filepath, index=False)
        
        print(f"   💾 Saved to: {filepath}")
        print(f"   📊 Records: {len(df):,}")
        print(f"   📅 Date range: {df['time'].min()} to {df['time'].max()}")
        print(f"   💰 Price range: ${df['close'].min():.2f} to ${df['close'].max():.2f}")
        
        return filepath
    
    def fetch_all_timeframes(self, start_date, end_date):
        """Fetch data for all timeframes"""
        print("🚀 Binance Historical Data Fetcher (Python)")
        print("=" * 50)
        print(f"📅 Fetching data from {start_date} to {end_date}")
        print(f"📊 Timeframes: {', '.join(self.timeframes.keys())}")
        print(f"🎯 Symbol: {self.symbol}")
        print()
        
        results = {
            'successful': [],
            'failed': []
        }
        
        for i, timeframe in enumerate(self.timeframes.keys()):
            print(f"\n{'='*20} {timeframe.upper()} ({'='*20}")
            
            try:
                # Fetch data
                klines_data = self.fetch_timeframe_data(timeframe, start_date, end_date)
                
                if klines_data is None:
                    print(f"   ❌ Failed to fetch {timeframe} data")
                    results['failed'].append(timeframe)
                    continue
                
                # Convert to DataFrame
                df = self.convert_to_dataframe(klines_data)
                
                if df.empty:
                    print(f"   ❌ No valid data for {timeframe}")
                    results['failed'].append(timeframe)
                    continue
                
                # Save to CSV
                filepath = self.save_to_csv(df, timeframe, start_date, end_date)
                
                if filepath:
                    results['successful'].append(timeframe)
                    print(f"   ✅ {timeframe} data completed successfully")
                else:
                    results['failed'].append(timeframe)
                
            except Exception as e:
                print(f"   ❌ Error processing {timeframe}: {e}")
                results['failed'].append(timeframe)
            
            # Add delay between timeframes (except for the last one)
            if i < len(self.timeframes) - 1:
                print(f"   ⏳ Waiting 10 seconds before next timeframe...")
                time.sleep(10)
        
        # Summary
        print(f"\n{'='*50}")
        print("📊 FETCH SUMMARY")
        print(f"{'='*50}")
        print(f"✅ Successful: {len(results['successful'])} timeframes")
        if results['successful']:
            print(f"   {', '.join(results['successful'])}")
        
        print(f"❌ Failed: {len(results['failed'])} timeframes")
        if results['failed']:
            print(f"   {', '.join(results['failed'])}")
        
        if results['successful']:
            print(f"\n🎉 Data fetching completed!")
            print(f"📁 CSV files saved in {self.data_dir}/")
            print(f"🔄 Next step: Run merge script to combine with existing data")
            print(f"💡 Command: python3 merge_historical_data.py")
        else:
            print(f"\n❌ No data was successfully fetched")
        
        return len(results['successful']) > 0

def main():
    """Main execution function"""
    # Date range for 2018-2020 (ending before existing data starts)
    start_date = datetime(2018, 1, 1)
    end_date = datetime(2019, 12, 31, 23, 59, 59)  # End of 2019
    
    print("🎯 BTC/USDT Historical Data Fetcher")
    print("This will fetch data from 2018-2020 to extend your existing dataset")
    print()
    print(f"📅 Date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
    print(f"⏱️ Estimated time: 30-60 minutes")
    print(f"🌐 Data source: Binance API")
    print()
    
    # Confirm with user
    response = input("Continue with data fetching? (y/N): ").strip().lower()
    if response != 'y':
        print("❌ Data fetching cancelled by user")
        return 1
    
    # Create fetcher and run
    fetcher = BinanceHistoricalFetcher()
    success = fetcher.fetch_all_timeframes(start_date, end_date)
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())

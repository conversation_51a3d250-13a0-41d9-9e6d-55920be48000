"""
Comprehensive system test for BTC/USDT Price Prediction System
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json
import time
import pandas as pd
import numpy as np
from pathlib import Path
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SystemTester:
    """Comprehensive system testing"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.test_results = {}
        
    def test_api_health(self):
        """Test API health endpoint"""
        logger.info("Testing API health...")
        
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                # Validate response structure
                required_fields = ['status', 'timestamp', 'models_loaded', 'last_data_update']
                missing_fields = [field for field in required_fields if field not in data]
                
                if missing_fields:
                    self.test_results['api_health'] = {
                        'status': 'FAIL',
                        'error': f'Missing fields: {missing_fields}'
                    }
                    return False
                
                # Check if models are loaded
                if data['models_loaded'] < 1:
                    self.test_results['api_health'] = {
                        'status': 'FAIL',
                        'error': 'No models loaded'
                    }
                    return False
                
                self.test_results['api_health'] = {
                    'status': 'PASS',
                    'models_loaded': data['models_loaded'],
                    'last_update': data['last_data_update']
                }
                logger.info(f"✅ API Health: {data['models_loaded']} models loaded")
                return True
                
            else:
                self.test_results['api_health'] = {
                    'status': 'FAIL',
                    'error': f'HTTP {response.status_code}'
                }
                return False
                
        except Exception as e:
            self.test_results['api_health'] = {
                'status': 'FAIL',
                'error': str(e)
            }
            logger.error(f"❌ API Health test failed: {e}")
            return False
    
    def test_current_price(self):
        """Test current price endpoint"""
        logger.info("Testing current price endpoint...")
        
        try:
            response = requests.get(f"{self.base_url}/current-price", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                # Validate response structure
                required_fields = ['price', 'timestamp', 'last_update']
                missing_fields = [field for field in required_fields if field not in data]
                
                if missing_fields:
                    self.test_results['current_price'] = {
                        'status': 'FAIL',
                        'error': f'Missing fields: {missing_fields}'
                    }
                    return False
                
                # Validate price is reasonable (BTC should be > $1000)
                if data['price'] < 1000:
                    self.test_results['current_price'] = {
                        'status': 'FAIL',
                        'error': f'Unrealistic price: ${data["price"]}'
                    }
                    return False
                
                self.test_results['current_price'] = {
                    'status': 'PASS',
                    'price': data['price'],
                    'timestamp': data['timestamp']
                }
                logger.info(f"✅ Current Price: ${data['price']:,.2f}")
                return True
                
            else:
                self.test_results['current_price'] = {
                    'status': 'FAIL',
                    'error': f'HTTP {response.status_code}'
                }
                return False
                
        except Exception as e:
            self.test_results['current_price'] = {
                'status': 'FAIL',
                'error': str(e)
            }
            logger.error(f"❌ Current price test failed: {e}")
            return False
    
    def test_predictions(self):
        """Test prediction endpoints for all models"""
        logger.info("Testing prediction endpoints...")
        
        models = ['NeuralNetwork', 'RandomForest', 'GradientBoosting']
        prediction_results = {}
        
        for model_name in models:
            try:
                payload = {
                    "model_name": model_name,
                    "include_confidence": True
                }
                
                response = requests.post(
                    f"{self.base_url}/predict",
                    json=payload,
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # Validate response structure
                    required_fields = ['timestamp', 'current_price', 'predictions', 'model_info']
                    missing_fields = [field for field in required_fields if field not in data]
                    
                    if missing_fields:
                        prediction_results[model_name] = {
                            'status': 'FAIL',
                            'error': f'Missing fields: {missing_fields}'
                        }
                        continue
                    
                    # Validate predictions for all horizons
                    expected_horizons = ['4h', '16h', '24h']
                    missing_horizons = [h for h in expected_horizons if h not in data['predictions']]
                    
                    if missing_horizons:
                        prediction_results[model_name] = {
                            'status': 'FAIL',
                            'error': f'Missing prediction horizons: {missing_horizons}'
                        }
                        continue
                    
                    # Validate prediction values are reasonable
                    current_price = data['current_price']
                    valid_predictions = True
                    
                    for horizon, pred_price in data['predictions'].items():
                        # Predictions should be within 50% of current price (reasonable for crypto)
                        if pred_price < current_price * 0.5 or pred_price > current_price * 1.5:
                            prediction_results[model_name] = {
                                'status': 'FAIL',
                                'error': f'Unrealistic prediction for {horizon}: ${pred_price:,.2f}'
                            }
                            valid_predictions = False
                            break
                    
                    if valid_predictions:
                        prediction_results[model_name] = {
                            'status': 'PASS',
                            'predictions': data['predictions'],
                            'current_price': current_price
                        }
                        logger.info(f"✅ {model_name} predictions: 4h=${data['predictions']['4h']:,.2f}, "
                                  f"16h=${data['predictions']['16h']:,.2f}, 24h=${data['predictions']['24h']:,.2f}")
                    
                else:
                    prediction_results[model_name] = {
                        'status': 'FAIL',
                        'error': f'HTTP {response.status_code}: {response.text}'
                    }
                    
            except Exception as e:
                prediction_results[model_name] = {
                    'status': 'FAIL',
                    'error': str(e)
                }
                logger.error(f"❌ {model_name} prediction test failed: {e}")
        
        self.test_results['predictions'] = prediction_results
        
        # Check if at least one model works
        successful_models = [m for m, r in prediction_results.items() if r['status'] == 'PASS']
        return len(successful_models) > 0
    
    def test_historical_data(self):
        """Test historical data endpoint"""
        logger.info("Testing historical data endpoint...")
        
        try:
            response = requests.get(f"{self.base_url}/historical-data?hours=24", timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                
                # Validate response structure
                required_fields = ['data', 'count', 'timeframe']
                missing_fields = [field for field in required_fields if field not in data]
                
                if missing_fields:
                    self.test_results['historical_data'] = {
                        'status': 'FAIL',
                        'error': f'Missing fields: {missing_fields}'
                    }
                    return False
                
                # Validate data points
                if data['count'] < 20:  # Should have at least 20 hours of data
                    self.test_results['historical_data'] = {
                        'status': 'FAIL',
                        'error': f'Insufficient data points: {data["count"]}'
                    }
                    return False
                
                # Validate data structure
                if not data['data'] or len(data['data']) == 0:
                    self.test_results['historical_data'] = {
                        'status': 'FAIL',
                        'error': 'Empty data array'
                    }
                    return False
                
                # Check first data point structure
                first_point = data['data'][0]
                required_point_fields = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
                missing_point_fields = [field for field in required_point_fields if field not in first_point]
                
                if missing_point_fields:
                    self.test_results['historical_data'] = {
                        'status': 'FAIL',
                        'error': f'Missing data point fields: {missing_point_fields}'
                    }
                    return False
                
                self.test_results['historical_data'] = {
                    'status': 'PASS',
                    'count': data['count'],
                    'timeframe': data['timeframe']
                }
                logger.info(f"✅ Historical Data: {data['count']} data points")
                return True
                
            else:
                self.test_results['historical_data'] = {
                    'status': 'FAIL',
                    'error': f'HTTP {response.status_code}'
                }
                return False
                
        except Exception as e:
            self.test_results['historical_data'] = {
                'status': 'FAIL',
                'error': str(e)
            }
            logger.error(f"❌ Historical data test failed: {e}")
            return False
    
    def test_models_endpoint(self):
        """Test models information endpoint"""
        logger.info("Testing models endpoint...")
        
        try:
            response = requests.get(f"{self.base_url}/models", timeout=10)
            
            if response.status_code == 200:
                models = response.json()
                
                if not isinstance(models, list) or len(models) == 0:
                    self.test_results['models_endpoint'] = {
                        'status': 'FAIL',
                        'error': 'No models returned'
                    }
                    return False
                
                # Validate model structure
                for model in models:
                    required_fields = ['name', 'is_trained', 'training_date', 'performance_metrics']
                    missing_fields = [field for field in required_fields if field not in model]
                    
                    if missing_fields:
                        self.test_results['models_endpoint'] = {
                            'status': 'FAIL',
                            'error': f'Missing model fields: {missing_fields}'
                        }
                        return False
                
                self.test_results['models_endpoint'] = {
                    'status': 'PASS',
                    'models_count': len(models),
                    'models': [m['name'] for m in models]
                }
                logger.info(f"✅ Models Endpoint: {len(models)} models available")
                return True
                
            else:
                self.test_results['models_endpoint'] = {
                    'status': 'FAIL',
                    'error': f'HTTP {response.status_code}'
                }
                return False
                
        except Exception as e:
            self.test_results['models_endpoint'] = {
                'status': 'FAIL',
                'error': str(e)
            }
            logger.error(f"❌ Models endpoint test failed: {e}")
            return False
    
    def test_web_dashboard(self):
        """Test web dashboard accessibility"""
        logger.info("Testing web dashboard...")
        
        try:
            response = requests.get(self.base_url, timeout=10)
            
            if response.status_code == 200:
                html_content = response.text
                
                # Check for essential HTML elements
                required_elements = [
                    '<title>BTC/USDT Price Prediction Dashboard</title>',
                    'id="currentPrice"',
                    'id="predictionsContainer"',
                    'id="priceChart"',
                    'id="modelSelect"'
                ]
                
                missing_elements = []
                for element in required_elements:
                    if element not in html_content:
                        missing_elements.append(element)
                
                if missing_elements:
                    self.test_results['web_dashboard'] = {
                        'status': 'FAIL',
                        'error': f'Missing HTML elements: {missing_elements}'
                    }
                    return False
                
                self.test_results['web_dashboard'] = {
                    'status': 'PASS',
                    'content_length': len(html_content)
                }
                logger.info("✅ Web Dashboard: HTML loaded successfully")
                return True
                
            else:
                self.test_results['web_dashboard'] = {
                    'status': 'FAIL',
                    'error': f'HTTP {response.status_code}'
                }
                return False
                
        except Exception as e:
            self.test_results['web_dashboard'] = {
                'status': 'FAIL',
                'error': str(e)
            }
            logger.error(f"❌ Web dashboard test failed: {e}")
            return False
    
    def run_all_tests(self):
        """Run all system tests"""
        logger.info("🚀 Starting comprehensive system tests...")
        logger.info("=" * 60)
        
        tests = [
            ('API Health', self.test_api_health),
            ('Current Price', self.test_current_price),
            ('Predictions', self.test_predictions),
            ('Historical Data', self.test_historical_data),
            ('Models Endpoint', self.test_models_endpoint),
            ('Web Dashboard', self.test_web_dashboard)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n🧪 Running {test_name} test...")
            
            try:
                if test_func():
                    passed_tests += 1
                    logger.info(f"✅ {test_name}: PASSED")
                else:
                    logger.error(f"❌ {test_name}: FAILED")
            except Exception as e:
                logger.error(f"❌ {test_name}: ERROR - {e}")
        
        # Generate summary
        logger.info("\n" + "=" * 60)
        logger.info("📊 TEST SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {total_tests - passed_tests}")
        logger.info(f"Success Rate: {(passed_tests / total_tests) * 100:.1f}%")
        
        if passed_tests == total_tests:
            logger.info("🎉 ALL TESTS PASSED! System is working correctly.")
        else:
            logger.warning("⚠️  Some tests failed. Check the logs above for details.")
        
        return self.test_results

def main():
    """Main test function"""
    print("🧪 BTC/USDT Price Prediction System - Comprehensive Testing")
    print("=" * 70)
    
    # Check if API is running
    tester = SystemTester()
    
    try:
        # Quick connectivity test
        response = requests.get(f"{tester.base_url}/health", timeout=5)
        if response.status_code != 200:
            print("❌ API is not responding. Please start the API first:")
            print("   python3 run_api.py")
            return
    except requests.exceptions.RequestException:
        print("❌ Cannot connect to API. Please start the API first:")
        print("   python3 run_api.py")
        return
    
    # Run all tests
    results = tester.run_all_tests()
    
    # Save results
    with open('test_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Test results saved to 'test_results.json'")
    
    return results

if __name__ == "__main__":
    main()

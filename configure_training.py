#!/usr/bin/env python3
"""
Training Configuration Script for BTC/USDT Price Prediction System

This script allows you to configure training data settings and provides
information about different training options.
"""

import sys
from pathlib import Path
from src.data_processing.data_loader import DataLoader
import pandas as pd

def get_data_info():
    """Get information about available data"""
    try:
        loader = DataLoader()
        df = loader.load_timeframe_data('1h')
        
        total_records = len(df)
        date_range = f"{df.index.min()} to {df.index.max()}"
        price_range = f"${df['close'].min():.2f} to ${df['close'].max():.2f}"
        
        return {
            'total_records': total_records,
            'date_range': date_range,
            'price_range': price_range,
            'data_years': (df.index.max() - df.index.min()).days / 365.25
        }
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return None

def show_training_options():
    """Display training configuration options"""
    print("🚀 BTC/USDT Price Prediction - Training Configuration")
    print("=" * 60)
    
    data_info = get_data_info()
    if not data_info:
        return
    
    print(f"📊 Available Data:")
    print(f"   Total Records: {data_info['total_records']:,}")
    print(f"   Date Range: {data_info['date_range']}")
    print(f"   Price Range: {data_info['price_range']}")
    print(f"   Coverage: {data_info['data_years']:.1f} years")
    print()
    
    print("🎯 Training Options:")
    print()
    
    print("1️⃣  FULL DATASET (RECOMMENDED)")
    print(f"   • Records: ALL {data_info['total_records']:,} records")
    print(f"   • Coverage: Complete {data_info['data_years']:.1f} years (2020-2025)")
    print("   • Benefits:")
    print("     ✅ Complete market cycle learning")
    print("     ✅ Better generalization across all market conditions")
    print("     ✅ Robust performance in bull/bear/sideways markets")
    print("     ✅ Historical pattern recognition")
    print("   • Trade-offs:")
    print("     ⚠️  Longer training time (60-90 minutes)")
    print("     ⚠️  Higher memory usage")
    print()
    
    print("2️⃣  RECENT DATA SUBSET")
    print("   • Records: Last 20,000 records (~2.3 years)")
    print("   • Coverage: Recent market data only")
    print("   • Benefits:")
    print("     ✅ Faster training (30-45 minutes)")
    print("     ✅ Lower memory usage")
    print("     ✅ Focus on recent market patterns")
    print("   • Trade-offs:")
    print("     ⚠️  Limited exposure to different market cycles")
    print("     ⚠️  May not generalize well to unseen market conditions")
    print()
    
    print("3️⃣  CUSTOM SUBSET")
    print("   • Records: User-defined number")
    print("   • Coverage: Configurable")
    print("   • Benefits:")
    print("     ✅ Flexible training time")
    print("     ✅ Customizable for specific needs")
    print("   • Trade-offs:")
    print("     ⚠️  Requires manual configuration")
    print()

def show_current_config():
    """Show current training configuration"""
    try:
        from config import MODEL_CONFIG
        
        print("📋 Current Training Configuration:")
        print("-" * 40)
        
        use_full = MODEL_CONFIG.get("use_full_dataset", True)
        max_records = MODEL_CONFIG.get("max_training_records", None)
        
        if use_full and max_records is None:
            print("✅ Mode: FULL DATASET")
            print("📊 Records: ALL available records")
            print("⏱️  Training Time: 60-90 minutes")
        elif use_full and max_records is not None:
            print("✅ Mode: LIMITED FULL DATASET")
            print(f"📊 Records: Last {max_records:,} records")
            print("⏱️  Training Time: Variable")
        else:
            print("✅ Mode: RECENT DATA SUBSET")
            print("📊 Records: Last 20,000 records")
            print("⏱️  Training Time: 30-45 minutes")
        
        print()
        print("Other settings:")
        print(f"   Prediction Horizons: {MODEL_CONFIG['prediction_horizons']} hours")
        print(f"   Features: {MODEL_CONFIG['features']}")
        print(f"   Train/Val/Test Split: {MODEL_CONFIG['train_split']}/{MODEL_CONFIG['val_split']}/{MODEL_CONFIG['test_split']}")
        
    except Exception as e:
        print(f"❌ Error reading configuration: {e}")

def update_config(use_full_dataset=True, max_records=None):
    """Update training configuration"""
    try:
        config_file = Path("config.py")
        
        # Read current config
        with open(config_file, 'r') as f:
            content = f.read()
        
        # Update the configuration
        if use_full_dataset and max_records is None:
            # Full dataset
            new_content = content.replace(
                '"use_full_dataset": False',
                '"use_full_dataset": True'
            ).replace(
                '"use_full_dataset": True',
                '"use_full_dataset": True'
            )
            new_content = new_content.replace(
                '"max_training_records": None',
                '"max_training_records": None'
            )
        elif use_full_dataset and max_records is not None:
            # Limited dataset
            new_content = content.replace(
                '"use_full_dataset": False',
                '"use_full_dataset": True'
            ).replace(
                '"max_training_records": None',
                f'"max_training_records": {max_records}'
            )
        else:
            # Recent subset
            new_content = content.replace(
                '"use_full_dataset": True',
                '"use_full_dataset": False'
            )
        
        # Write updated config
        with open(config_file, 'w') as f:
            f.write(new_content)
        
        print("✅ Configuration updated successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error updating configuration: {e}")
        return False

def main():
    """Main function"""
    if len(sys.argv) == 1:
        # Show information and current config
        show_training_options()
        print()
        show_current_config()
        print()
        print("💡 Usage:")
        print("   python3 configure_training.py --full      # Use full dataset")
        print("   python3 configure_training.py --recent    # Use recent 20K records")
        print("   python3 configure_training.py --limit N   # Use last N records")
        print("   python3 configure_training.py --info      # Show this information")
        
    elif "--full" in sys.argv:
        print("🔧 Configuring for FULL DATASET training...")
        if update_config(use_full_dataset=True, max_records=None):
            print("✅ Now configured to use ALL available data")
            print("⏱️  Training time: 60-90 minutes")
            
    elif "--recent" in sys.argv:
        print("🔧 Configuring for RECENT DATA training...")
        if update_config(use_full_dataset=False):
            print("✅ Now configured to use recent 20,000 records")
            print("⏱️  Training time: 30-45 minutes")
            
    elif "--limit" in sys.argv:
        try:
            idx = sys.argv.index("--limit")
            if idx + 1 < len(sys.argv):
                max_records = int(sys.argv[idx + 1])
                print(f"🔧 Configuring for LIMITED DATASET training ({max_records:,} records)...")
                if update_config(use_full_dataset=True, max_records=max_records):
                    print(f"✅ Now configured to use last {max_records:,} records")
                    estimated_time = max(30, min(90, max_records / 1000))
                    print(f"⏱️  Estimated training time: {estimated_time:.0f}-{estimated_time*1.5:.0f} minutes")
            else:
                print("❌ Please specify number of records: --limit N")
        except (ValueError, IndexError):
            print("❌ Invalid limit value. Please use: --limit N")
            
    elif "--info" in sys.argv:
        show_training_options()
        print()
        show_current_config()
        
    else:
        print("❌ Unknown option. Use --help for usage information")

if __name__ == "__main__":
    main()

#!/bin/bash

# Complete script to extend dataset from 2018-2025
# This script fetches 2018-2020 data and merges it with existing 2020-2025 data

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}🚀 BTC Dataset Extension (2018-2025)${NC}"
    echo -e "${PURPLE}================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Main function
main() {
    print_header
    echo
    echo "This script will:"
    echo "1. 📥 Fetch historical BTC/USDT data from 2018-2020"
    echo "2. 🔗 Merge with existing 2020-2025 data"
    echo "3. 🤖 Retrain models with extended 7.5-year dataset"
    echo
    echo "Timeframes: 15m, 1h, 4h, 1d, 1w, 1M"
    echo "Expected improvement: ~40-50% more training data"
    echo
    
    # Confirm with user
    read -p "Continue with dataset extension? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Operation cancelled by user"
        exit 0
    fi
    
    # Check prerequisites
    print_status "Checking prerequisites..."
    
    # Check Node.js
    if command_exists node; then
        NODE_VERSION=$(node --version)
        print_success "Node.js found: $NODE_VERSION"
    else
        print_error "Node.js not found. Please install Node.js 18+"
        exit 1
    fi
    
    # Check Python
    if command_exists python3; then
        PYTHON_VERSION=$(python3 --version)
        print_success "Python found: $PYTHON_VERSION"
    else
        print_error "Python 3 not found"
        exit 1
    fi
    
    # Check if historical data fetcher exists
    if [ ! -d "historical-data-fetcher" ]; then
        print_error "Historical data fetcher directory not found"
        exit 1
    fi
    
    print_success "All prerequisites met"
    echo
    
    # Step 1: Install npm dependencies
    print_status "Installing npm dependencies..."
    cd historical-data-fetcher
    
    if [ ! -d "node_modules" ]; then
        if npm install; then
            print_success "npm dependencies installed"
        else
            print_error "Failed to install npm dependencies"
            exit 1
        fi
    else
        print_success "npm dependencies already installed"
    fi
    
    cd ..
    echo
    
    # Step 2: Fetch historical data
    print_status "Fetching historical data from 2018-2020..."
    print_warning "This may take 30-60 minutes depending on your internet connection"
    print_warning "The script will add delays between requests to avoid rate limiting"
    echo
    
    cd historical-data-fetcher
    if node fetch_2018_2020.js; then
        print_success "Historical data fetching completed"
    else
        print_error "Failed to fetch historical data"
        cd ..
        exit 1
    fi
    cd ..
    echo
    
    # Step 3: Merge datasets
    print_status "Merging datasets..."
    if python3 merge_historical_data.py; then
        print_success "Dataset merging completed"
    else
        print_error "Failed to merge datasets"
        exit 1
    fi
    echo
    
    # Step 4: Verify merged data
    print_status "Verifying merged datasets..."
    python3 -c "
import pandas as pd
import os

timeframes = ['15m', '1h', '4h', '1d', '1w', '1M']
print('📊 Dataset Verification:')
print('=' * 40)

for tf in timeframes:
    file_path = f'data/{tf}_01012018_3062025.csv'
    if os.path.exists(file_path):
        df = pd.read_csv(file_path)
        df['time'] = pd.to_datetime(df['time'])
        print(f'{tf:>3}: {len(df):>6,} records | {df[\"time\"].min()} to {df[\"time\"].max()}')
    else:
        print(f'{tf:>3}: ❌ File not found')

print()
print('✅ Verification completed')
"
    echo
    
    # Step 5: Ask about retraining
    print_status "Dataset extension completed successfully!"
    echo
    print_success "📈 Your dataset now covers 7.5 years (2018-2025)"
    print_success "🎯 This should significantly improve model accuracy"
    echo
    
    read -p "Would you like to retrain the models now? This will take 90-120 minutes. (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Starting model retraining with extended dataset..."
        echo
        
        if python3 train_models.py; then
            print_success "Model retraining completed successfully!"
            echo
            print_success "🎉 Your models are now trained on 7.5 years of data!"
            print_success "🚀 Start the dashboard to see improved predictions:"
            print_success "   ./run.sh"
        else
            print_error "Model retraining failed"
            exit 1
        fi
    else
        print_status "Model retraining skipped"
        print_warning "Remember to retrain models later with: python3 train_models.py"
    fi
    
    echo
    print_header
    echo -e "${GREEN}🎉 Dataset Extension Completed Successfully!${NC}"
    echo
    echo "📊 Summary:"
    echo "   • Extended dataset from 5.4 years to 7.5 years"
    echo "   • Added 2018-2020 historical data for all timeframes"
    echo "   • Merged datasets are available in data/ directory"
    echo "   • Models can now learn from complete market cycles"
    echo
    echo "🚀 Next steps:"
    echo "   • Start dashboard: ./run.sh"
    echo "   • Test predictions with extended training data"
    echo "   • Monitor improved accuracy across different market conditions"
    echo
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "BTC Dataset Extension Script (2018-2025)"
        echo ""
        echo "This script extends your BTC/USDT dataset by fetching historical"
        echo "data from 2018-2020 and merging it with existing 2020-2025 data."
        echo ""
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --fetch-only   Only fetch data, don't merge or retrain"
        echo "  --merge-only   Only merge existing fetched data"
        echo ""
        echo "The complete process includes:"
        echo "  1. Fetch 2018-2020 data for all timeframes (15m, 1h, 4h, 1d, 1w, 1M)"
        echo "  2. Merge with existing 2020-2025 data"
        echo "  3. Optionally retrain models with extended dataset"
        echo ""
        exit 0
        ;;
    --fetch-only)
        print_header
        print_status "Fetch-only mode: Fetching 2018-2020 data..."
        cd historical-data-fetcher
        node fetch_2018_2020.js
        cd ..
        print_success "Data fetching completed. Run with --merge-only to merge datasets."
        exit 0
        ;;
    --merge-only)
        print_header
        print_status "Merge-only mode: Merging existing datasets..."
        python3 merge_historical_data.py
        exit 0
        ;;
    "")
        main
        ;;
    *)
        print_error "Unknown option: $1"
        echo "Use --help for usage information"
        exit 1
        ;;
esac

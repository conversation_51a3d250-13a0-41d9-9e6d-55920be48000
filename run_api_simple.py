#!/usr/bin/env python3
"""
Simple API launcher for BTC/USDT Price Prediction System
This version avoids gradient boosting models that require OpenMP
"""

import uvicorn
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def main():
    """Main function to start the API server"""
    try:
        print("🚀 Starting BTC/USDT Price Prediction API (Simple Mode)")
        print("=" * 60)
        print("📊 Loading models and data...")
        
        # Import the app here to handle any import issues
        from src.api.main_simple import app
        
        print("✅ API initialized successfully!")
        print()
        print("🌐 Dashboard: http://localhost:8000")
        print("📊 API Docs: http://localhost:8000/docs")
        print("📈 Health Check: http://localhost:8000/health")
        print()
        print("⚠️  Press Ctrl+C to stop the server")
        print()
        
        # Start the server
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info",
            access_log=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        print(f"\n❌ Error: {e}")
        print("\n💡 Try installing missing dependencies:")
        print("   pip install -r requirements.txt")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())

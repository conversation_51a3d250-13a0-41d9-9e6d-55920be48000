#!/bin/bash

# BTC/USDT Price Prediction System - Run Script
# This script starts the web dashboard and API server

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}🚀 BTC/USDT Price Prediction${NC}"
    echo -e "${PURPLE}================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if a Python package is installed
python_package_exists() {
    python3 -c "import $1" >/dev/null 2>&1
}

# Function to check port availability
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 1  # Port is in use
    else
        return 0  # Port is available
    fi
}

# Function to kill process on port
kill_port() {
    local port=$1
    local pid=$(lsof -ti:$port)
    if [ ! -z "$pid" ]; then
        print_warning "Killing existing process on port $port (PID: $pid)"
        kill -9 $pid 2>/dev/null || true
        sleep 2
    fi
}

# Main function
main() {
    print_header
    
    # Check if Python 3 is installed
    print_status "Checking Python installation..."
    if command_exists python3; then
        PYTHON_VERSION=$(python3 --version)
        print_success "Found $PYTHON_VERSION"
    else
        print_error "Python 3 is not installed. Please install Python 3.8 or higher."
        exit 1
    fi
    
    # Check if required packages are installed
    print_status "Checking Python dependencies..."
    required_packages=("pandas" "numpy" "sklearn" "fastapi" "uvicorn")
    missing_packages=()
    
    for package in "${required_packages[@]}"; do
        if python_package_exists "$package"; then
            print_success "$package is installed"
        else
            missing_packages+=("$package")
        fi
    done
    
    if [ ${#missing_packages[@]} -ne 0 ]; then
        print_warning "Missing packages: ${missing_packages[*]}"
        print_status "Installing missing dependencies..."
        if pip3 install -r requirements.txt; then
            print_success "Dependencies installed successfully"
        else
            print_error "Failed to install dependencies"
            exit 1
        fi
    fi
    
    # Check for data files
    print_status "Checking for data files..."
    if [ ! -f "data/1h_3112020_3062025.csv" ]; then
        print_error "Required data file not found: data/1h_3112020_3062025.csv"
        print_warning "Please run the historical data fetcher or place data files in the data/ directory"
        print_warning "See DATA_FETCHING_GUIDE.md for instructions"
        exit 1
    else
        print_success "Data files found"
    fi
    
    # Check for trained models
    print_status "Checking for trained models..."
    model_dirs=("models/NeuralNetwork" "models/RandomForest" "models/GradientBoosting")
    missing_models=()
    
    for model_dir in "${model_dirs[@]}"; do
        if [ -d "$model_dir" ] && [ "$(ls -A $model_dir/*.joblib 2>/dev/null | wc -l)" -gt 0 ]; then
            print_success "$(basename $model_dir) models found"
        else
            missing_models+=("$(basename $model_dir)")
        fi
    done
    
    if [ ${#missing_models[@]} -ne 0 ]; then
        print_warning "Missing models: ${missing_models[*]}"
        echo ""
        read -p "Would you like to train the models now? This will take 60-90 minutes. (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            print_status "Starting model training..."
            if python3 train_models.py; then
                print_success "Model training completed successfully"
            else
                print_error "Model training failed"
                exit 1
            fi
        else
            print_warning "Some models are missing. The dashboard may have limited functionality."
            echo ""
            read -p "Continue anyway? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                print_status "Exiting. Run 'python3 train_models.py' to train models first."
                exit 0
            fi
        fi
    fi
    
    # Check if port 8000 is available
    print_status "Checking port availability..."
    if ! check_port 8000; then
        print_warning "Port 8000 is already in use"
        echo ""
        read -p "Kill existing process and continue? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            kill_port 8000
        else
            print_error "Cannot start server. Port 8000 is in use."
            exit 1
        fi
    fi
    
    # Create logs directory if it doesn't exist
    mkdir -p logs
    
    # Start the API server
    print_status "Starting BTC/USDT Price Prediction API server..."
    print_status "This may take a few moments to load models and data..."

    echo ""
    print_success "🌐 Dashboard will be available at: ${CYAN}http://localhost:8000${NC}"
    print_success "📊 API documentation at: ${CYAN}http://localhost:8000/docs${NC}"
    print_success "📈 Training info at: ${CYAN}http://localhost:8000/training-info${NC}"
    echo ""
    print_warning "Press ${YELLOW}Ctrl+C${NC} to stop the server"
    echo ""

    # Try the full API first, fall back to simple API if it fails
    if python3 run_api.py 2>/dev/null; then
        echo "Full API started successfully"
    else
        print_warning "Full API failed (likely missing OpenMP), starting simple API..."
        python3 run_api_simple.py
    fi
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "BTC/USDT Price Prediction System - Run Script"
        echo ""
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --check, -c    Check system status without starting"
        echo "  --train, -t    Train models before starting"
        echo "  --force, -f    Force start (kill existing processes)"
        echo "  --simple, -s   Start simple API (without gradient boosting)"
        echo "  --full         Start full API (requires OpenMP for gradient boosting)"
        echo ""
        echo "Examples:"
        echo "  $0              # Start the dashboard"
        echo "  $0 --check     # Check if everything is ready"
        echo "  $0 --train     # Train models then start"
        echo "  $0 --force     # Force start (kill port 8000)"
        echo "  $0 --simple    # Start simple API (recommended for macOS)"
        echo "  $0 --full      # Start full API with all models"
        exit 0
        ;;
    --check|-c)
        print_header
        print_status "System status check..."
        # Run checks without starting server
        python3 -c "
import sys
sys.path.append('.')
try:
    from src.data_processing.data_loader import DataLoader
    loader = DataLoader()
    df = loader.load_timeframe_data('1h')
    print(f'✅ Data loaded: {len(df):,} records')
    
    import os
    models_found = []
    for model in ['NeuralNetwork', 'RandomForest', 'GradientBoosting']:
        model_dir = f'models/{model}'
        if os.path.exists(model_dir):
            joblib_files = [f for f in os.listdir(model_dir) if f.endswith('.joblib')]
            if joblib_files:
                models_found.append(model)
    
    print(f'✅ Models available: {models_found}')
    print('🎉 System is ready to run!')
    
except Exception as e:
    print(f'❌ System check failed: {e}')
    sys.exit(1)
"
        exit 0
        ;;
    --train|-t)
        print_header
        print_status "Training models before starting..."
        if python3 train_models.py; then
            print_success "Training completed. Starting server..."
            main
        else
            print_error "Training failed"
            exit 1
        fi
        ;;
    --force|-f)
        print_header
        print_status "Force starting (killing existing processes)..."
        kill_port 8000
        main
        ;;
    --simple|-s)
        print_header
        print_status "Starting simple API (Neural Network + Random Forest only)..."
        if ! check_port 8000; then
            kill_port 8000
        fi
        python3 run_api_simple.py
        ;;
    --full)
        print_header
        print_status "Starting full API (all models including gradient boosting)..."
        if ! check_port 8000; then
            kill_port 8000
        fi
        python3 run_api.py
        ;;
    "")
        main
        ;;
    *)
        print_error "Unknown option: $1"
        echo "Use --help for usage information"
        exit 1
        ;;
esac

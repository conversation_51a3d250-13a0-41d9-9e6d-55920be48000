// BTC/USDT Price Prediction Dashboard JavaScript

// Global variables
let priceChart = null;
let performanceChart = null;
let hourlyPredictionChart = null;
let currentData = null;
let loadingModal = null;
let retrainModal = null;
let metricsModal = null;
let autoRefreshInterval = null;

// Initialize dashboard when page loads
document.addEventListener("DOMContentLoaded", function () {
  console.log("DOM loaded, initializing dashboard...");

  // Initialize loading modal
  try {
    const modalElement = document.getElementById("loadingModal");
    if (modalElement && typeof bootstrap !== "undefined") {
      loadingModal = new bootstrap.Modal(modalElement);
      console.log("Loading modal initialized successfully");
    } else {
      console.warn("Bootstrap or modal element not found, using fallback");
    }
  } catch (error) {
    console.error("Failed to initialize loading modal:", error);
  }

  // Initialize modals
  try {
    retrainModal = new bootstrap.Modal(document.getElementById("retrainModal"));
    metricsModal = new bootstrap.Modal(document.getElementById("metricsModal"));
    console.log("Modals initialized successfully");
  } catch (error) {
    console.error("Failed to initialize modals:", error);
  }

  // Start dashboard initialization
  initializeDashboard();

  // Auto-refresh every 5 minutes
  setInterval(refreshData, 5 * 60 * 1000);

  // Test control panel
  console.log("Control panel elements:");
  console.log("- modelSelect:", document.getElementById("modelSelect"));
  console.log("- predictionHours:", document.getElementById("predictionHours"));
  console.log("- autoRefresh:", document.getElementById("autoRefresh"));
});

// Initialize dashboard
async function initializeDashboard() {
  showLoading();

  try {
    console.log("Starting dashboard initialization...");
    const startTime = performance.now();

    // Load critical data first (current price and predictions)
    console.log("Loading critical data...");
    await Promise.all([loadCurrentPrice(), loadPredictions()]);
    console.log("Critical data loaded");

    // Load secondary data in parallel
    console.log("Loading secondary data...");
    await Promise.all([
      loadHistoricalData(),
      loadModelInfo(),
      loadTrainingInfo(),
      loadHourlyPredictions(),
    ]);
    console.log("Secondary data loaded");

    updateLastUpdateTime();

    const endTime = performance.now();
    const loadTime = Math.round(endTime - startTime);
    console.log(`Dashboard initialization completed in ${loadTime}ms`);

    // Show load time in the UI
    const updateTimeElement = document.getElementById("updateTime");
    if (updateTimeElement) {
      updateTimeElement.innerHTML += ` <small class="text-muted">(loaded in ${loadTime}ms)</small>`;
    }
  } catch (error) {
    console.error("Failed to initialize dashboard:", error);
    showError("Failed to load dashboard data: " + error.message);
  } finally {
    hideLoading();
  }
}

// Load current price
async function loadCurrentPrice() {
  try {
    const response = await fetch("/current-price");
    const data = await response.json();

    if (response.ok) {
      document.getElementById("currentPrice").textContent = `$${formatNumber(
        data.price
      )}`;
      document.getElementById("priceTimestamp").textContent = formatDateTime(
        data.timestamp
      );
      currentData = data;
    } else {
      throw new Error(data.detail || "Failed to load current price");
    }
  } catch (error) {
    console.error("Error loading current price:", error);
    document.getElementById("currentPrice").textContent = "Error";
  }
}

// Load predictions
async function loadPredictions() {
  const modelSelect = document.getElementById("modelSelect");
  const modelName = modelSelect ? modelSelect.value : "NeuralNetwork";
  const container = document.getElementById("predictionsContainer");

  try {
    console.log(`Loading predictions for model: ${modelName}`);

    // Show loading indicator
    if (container) {
      container.innerHTML = `
        <div class="col-12">
          <div class="card">
            <div class="card-body text-center">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <p class="mt-2 mb-0">Loading ${modelName} predictions...</p>
            </div>
          </div>
        </div>
      `;
    }

    // Add timeout to prevent hanging
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    const response = await fetch("/predict", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model_name: modelName,
        include_confidence: true,
      }),
      signal: controller.signal,
    });

    clearTimeout(timeoutId);
    const data = await response.json();
    console.log("Prediction response received");

    if (response.ok) {
      displayPredictions(data);
      console.log("Predictions displayed successfully");
    } else {
      throw new Error(
        data.detail || `HTTP ${response.status}: Failed to load predictions`
      );
    }
  } catch (error) {
    if (error.name === "AbortError") {
      console.error("Prediction request timed out");
      error.message = "Request timed out - please try again";
    } else {
      console.error("Error loading predictions:", error);
    }

    const container = document.getElementById("predictionsContainer");
    if (container) {
      container.innerHTML = `<div class="col-12"><div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle"></i>
        Failed to load predictions: ${error.message}
        <button class="btn btn-sm btn-outline-primary ms-2" onclick="loadPredictions()">Retry</button>
      </div></div>`;
    }
    // Don't re-throw for timeout errors to prevent blocking other loads
    if (error.name !== "AbortError") {
      throw error;
    }
  }
}

// Display predictions
function displayPredictions(data) {
  const container = document.getElementById("predictionsContainer");
  const horizons = ["4h", "16h", "24h"];
  const horizonLabels = {
    "4h": "4 Hours",
    "16h": "16 Hours",
    "24h": "24 Hours",
  };

  let html = "";

  horizons.forEach((horizon) => {
    if (data.predictions[horizon]) {
      const prediction = data.predictions[horizon];
      const currentPrice = data.current_price;
      const change = prediction - currentPrice;
      const changePercent = (change / currentPrice) * 100;
      const isPositive = change >= 0;

      // Confidence interval
      let confidenceHtml = "";
      if (data.confidence_intervals && data.confidence_intervals[horizon]) {
        const ci = data.confidence_intervals[horizon];
        confidenceHtml = `
                    <small class="text-muted d-block">
                        95% CI: $${formatNumber(ci.lower)} - $${formatNumber(
          ci.upper
        )}
                    </small>
                `;
      }

      html += `
                <div class="col-md-4">
                    <div class="card border-${
                      isPositive ? "success" : "danger"
                    }">
                        <div class="card-body text-center">
                            <h6 class="card-title">${
                              horizonLabels[horizon]
                            }</h6>
                            <h4 class="text-${
                              isPositive ? "success" : "danger"
                            }">
                                $${formatNumber(prediction)}
                            </h4>
                            <p class="mb-1">
                                <span class="badge bg-${
                                  isPositive ? "success" : "danger"
                                }">
                                    ${isPositive ? "+" : ""}${formatNumber(
        change
      )}
                                    (${
                                      isPositive ? "+" : ""
                                    }${changePercent.toFixed(2)}%)
                                </span>
                            </p>
                            ${confidenceHtml}
                        </div>
                    </div>
                </div>
            `;
    }
  });

  container.innerHTML = html;
}

// Load historical data and create chart
async function loadHistoricalData() {
  try {
    const response = await fetch("/historical-data?hours=168"); // 7 days
    const data = await response.json();

    if (response.ok) {
      createPriceChart(data.data);
    } else {
      throw new Error(data.detail || "Failed to load historical data");
    }
  } catch (error) {
    console.error("Error loading historical data:", error);
  }
}

// Create price chart
function createPriceChart(data) {
  const ctx = document.getElementById("priceChart").getContext("2d");

  if (priceChart) {
    priceChart.destroy();
  }

  const labels = data.map((item) => new Date(item.timestamp));
  const prices = data.map((item) => item.close);

  priceChart = new Chart(ctx, {
    type: "line",
    data: {
      labels: labels,
      datasets: [
        {
          label: "BTC/USDT Price",
          data: prices,
          borderColor: "#007bff",
          backgroundColor: "rgba(0, 123, 255, 0.1)",
          borderWidth: 2,
          fill: true,
          tension: 0.1,
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        x: {
          type: "time",
          time: {
            unit: "hour",
            displayFormats: {
              hour: "MMM dd HH:mm",
            },
          },
        },
        y: {
          beginAtZero: false,
          ticks: {
            callback: function (value) {
              return "$" + formatNumber(value);
            },
          },
        },
      },
      plugins: {
        tooltip: {
          callbacks: {
            label: function (context) {
              return "Price: $" + formatNumber(context.parsed.y);
            },
          },
        },
      },
    },
  });
}

// Load model information
async function loadModelInfo() {
  try {
    const response = await fetch("/models");
    const models = await response.json();

    if (response.ok) {
      displayModelInfo(models);
      createPerformanceChart(models);
    } else {
      throw new Error("Failed to load model information");
    }
  } catch (error) {
    console.error("Error loading model info:", error);
  }
}

// Load training information
async function loadTrainingInfo() {
  try {
    const response = await fetch("/training-info");
    const trainingInfo = await response.json();

    if (response.ok) {
      displayTrainingInfo(trainingInfo);
    } else {
      throw new Error("Failed to load training information");
    }
  } catch (error) {
    console.error("Error loading training info:", error);
  }
}

// Display model information
function displayModelInfo(models) {
  const container = document.getElementById("modelInfoContainer");
  let html = "";

  models.forEach((model) => {
    const trainingDate = model.training_date
      ? formatDateTime(model.training_date)
      : "Not available";

    // Get best metric for display
    let bestMetric = "N/A";
    if (
      model.performance_metrics &&
      Object.keys(model.performance_metrics).length > 0
    ) {
      const firstHorizon = Object.keys(model.performance_metrics)[0];
      const metrics = model.performance_metrics[firstHorizon];
      if (metrics.r2 !== undefined) {
        bestMetric = `R² = ${metrics.r2.toFixed(4)}`;
      }
    }

    html += `
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-robot"></i> ${model.name}
                        </h6>
                        <p class="card-text">
                            <small class="text-muted">
                                Status: <span class="badge bg-${
                                  model.is_trained ? "success" : "warning"
                                }">
                                    ${
                                      model.is_trained
                                        ? "Trained"
                                        : "Not Trained"
                                    }
                                </span><br>
                                Trained: ${trainingDate}<br>
                                Best Score: ${bestMetric}
                            </small>
                        </p>
                    </div>
                </div>
            </div>
        `;
  });

  container.innerHTML = html;
}

// Display training information
function displayTrainingInfo(trainingInfo) {
  // Update the current price section with training data info
  const priceTimestamp = document.getElementById("priceTimestamp");
  if (priceTimestamp && trainingInfo.dataset_info) {
    const latestPrice = trainingInfo.dataset_info.price_range.latest_price;
    const latestDate = trainingInfo.dataset_info.date_range.end;
    priceTimestamp.innerHTML = `
      Latest: ${latestDate}<br>
      <small>Dataset: ${trainingInfo.dataset_info.total_records.toLocaleString()} records
      (${trainingInfo.dataset_info.date_range.start} to ${
      trainingInfo.dataset_info.date_range.end
    })</small>
    `;
  }

  // Add training info to model info section
  const modelContainer = document.getElementById("modelInfoContainer");
  if (modelContainer && trainingInfo.training_data) {
    const trainingCard = `
      <div class="col-md-12 mb-3">
        <div class="card border-info">
          <div class="card-header bg-info text-white">
            <h6 class="mb-0">
              <i class="fas fa-database"></i> Latest Training Data Information
            </h6>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <h6>Dataset Overview</h6>
                <ul class="list-unstyled">
                  <li><strong>Total Records:</strong> ${trainingInfo.dataset_info.total_records.toLocaleString()}</li>
                  <li><strong>Date Range:</strong> ${
                    trainingInfo.dataset_info.date_range.start
                  } to ${trainingInfo.dataset_info.date_range.end}</li>
                  <li><strong>Price Range:</strong> $${trainingInfo.dataset_info.price_range.start_price.toLocaleString()} - $${trainingInfo.dataset_info.price_range.latest_price.toLocaleString()}</li>
                </ul>
              </div>
              <div class="col-md-6">
                <h6>Training Data Used</h6>
                <ul class="list-unstyled">
                  <li><strong>Records Used:</strong> ${trainingInfo.training_data.records_used.toLocaleString()}</li>
                  <li><strong>Training Range:</strong> ${
                    trainingInfo.training_data.date_range.start
                  } to ${trainingInfo.training_data.date_range.end}</li>
                  <li><strong>Coverage:</strong> ${
                    trainingInfo.training_data.coverage_years
                  } years of recent data</li>
                </ul>
              </div>
            </div>
            ${
              trainingInfo.models && trainingInfo.models.length > 0
                ? `
            <hr>
            <h6>Model Training Dates</h6>
            <div class="row">
              ${trainingInfo.models
                .map(
                  (model) => `
                <div class="col-md-4">
                  <div class="badge bg-success me-2 mb-2">
                    ${model.name}: ${model.training_date}
                  </div>
                </div>
              `
                )
                .join("")}
            </div>
            `
                : ""
            }
          </div>
        </div>
      </div>
    `;

    // Insert at the beginning of the model container
    modelContainer.insertAdjacentHTML("afterbegin", trainingCard);
  }
}

// Create performance chart
function createPerformanceChart(models) {
  const ctx = document.getElementById("performanceChart").getContext("2d");

  if (performanceChart) {
    performanceChart.destroy();
  }

  const modelNames = models.map((m) => m.name);
  const r2Scores = models.map((model) => {
    if (
      model.performance_metrics &&
      Object.keys(model.performance_metrics).length > 0
    ) {
      const firstHorizon = Object.keys(model.performance_metrics)[0];
      return model.performance_metrics[firstHorizon].r2 || 0;
    }
    return 0;
  });

  performanceChart = new Chart(ctx, {
    type: "bar",
    data: {
      labels: modelNames,
      datasets: [
        {
          label: "R² Score",
          data: r2Scores,
          backgroundColor: ["#007bff", "#28a745", "#ffc107"],
          borderColor: ["#0056b3", "#1e7e34", "#e0a800"],
          borderWidth: 1,
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          max: 1,
          ticks: {
            callback: function (value) {
              return value.toFixed(2);
            },
          },
        },
      },
      plugins: {
        tooltip: {
          callbacks: {
            label: function (context) {
              return "R² Score: " + context.parsed.y.toFixed(4);
            },
          },
        },
      },
    },
  });
}

// Utility functions
function formatNumber(num) {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(2) + "M";
  } else if (num >= 1000) {
    return (num / 1000).toFixed(2) + "K";
  } else {
    return num.toLocaleString("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  }
}

function formatDateTime(dateString) {
  const date = new Date(dateString);
  // Convert to UTC+7 timezone
  const utc7Date = new Date(date.getTime() + 7 * 60 * 60 * 1000);
  return utc7Date.toLocaleString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    timeZone: "Asia/Bangkok", // UTC+7
  });
}

function showLoading() {
  console.log("Showing loading modal...");
  if (loadingModal) {
    loadingModal.show();
  } else {
    console.error("Loading modal not initialized");
  }
}

function hideLoading() {
  console.log("Hiding loading modal...");
  if (loadingModal) {
    loadingModal.hide();
  } else {
    console.error("Loading modal not initialized");
  }

  // Fallback: hide modal manually if bootstrap modal fails
  setTimeout(() => {
    const modalElement = document.getElementById("loadingModal");
    if (modalElement) {
      modalElement.style.display = "none";
      modalElement.classList.remove("show");
      document.body.classList.remove("modal-open");

      // Remove backdrop if it exists
      const backdrop = document.querySelector(".modal-backdrop");
      if (backdrop) {
        backdrop.remove();
      }
    }
  }, 100);
}

function showError(message) {
  // Create a simple alert for errors
  const alertDiv = document.createElement("div");
  alertDiv.className = "alert alert-danger alert-dismissible fade show";
  alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

  // Insert at the top of the container
  const container = document.querySelector(".container-fluid");
  container.insertBefore(alertDiv, container.firstChild);

  // Auto-dismiss after 5 seconds
  setTimeout(() => {
    if (alertDiv.parentNode) {
      alertDiv.remove();
    }
  }, 5000);
}

function updateLastUpdateTime() {
  const now = new Date();
  // Convert to UTC+7
  const utc7Time = new Date(now.getTime() + 7 * 60 * 60 * 1000);
  document.getElementById("updateTime").textContent = formatDateTime(
    utc7Time.toISOString()
  );
}

// Event handlers
async function refreshData() {
  await initializeDashboard();
}

async function updatePredictions() {
  // Sync the main model selector with this one
  const predictionModelSelect = document.getElementById(
    "modelSelectPredictions"
  );
  const mainModelSelect = document.getElementById("modelSelect");

  if (predictionModelSelect && mainModelSelect) {
    mainModelSelect.value = predictionModelSelect.value;
  }

  showLoading();
  try {
    await Promise.all([loadPredictions(), loadHourlyPredictions()]);
  } catch (error) {
    console.error("Failed to update predictions:", error);
    showError("Failed to update predictions");
  } finally {
    hideLoading();
  }
}

// Load hourly predictions
async function loadHourlyPredictions() {
  const hours = document.getElementById("predictionHours")?.value || 24;
  const modelSelect = document.getElementById("modelSelect");
  const selectedModel = modelSelect?.value || "NeuralNetwork";

  try {
    console.log(
      `Loading hourly predictions for ${hours} hours using ${selectedModel} model...`
    );
    const response = await fetch(
      `/hourly-predictions/${hours}?model=${selectedModel}`
    );
    const data = await response.json();

    if (response.ok) {
      console.log("Hourly predictions loaded successfully:", data);
      displayHourlyPredictions(data);
    } else {
      console.error("API error:", data);
      throw new Error(data.detail || "Failed to load hourly predictions");
    }
  } catch (error) {
    console.error("Error loading hourly predictions:", error);
    // Show error in the chart area
    const chartContainer = document.getElementById(
      "hourlyPredictionChart"
    )?.parentElement;
    if (chartContainer) {
      chartContainer.innerHTML = `
        <div class="alert alert-warning">
          <i class="fas fa-exclamation-triangle"></i>
          Failed to load hourly predictions: ${error.message}
          <button class="btn btn-sm btn-outline-primary ms-2" onclick="loadHourlyPredictions()">Retry</button>
        </div>
      `;
    }
  }
}

// Display hourly predictions chart
function displayHourlyPredictions(data) {
  const ctx = document
    .getElementById("hourlyPredictionChart")
    ?.getContext("2d");
  if (!ctx) {
    console.error("Canvas element 'hourlyPredictionChart' not found");
    return;
  }

  // Destroy existing chart
  if (hourlyPredictionChart) {
    hourlyPredictionChart.destroy();
  }

  console.log("Creating hourly predictions chart with data:", data);

  const labels = data.predictions.map((p) => {
    const date = new Date(p.timestamp);
    return date.toLocaleString("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      timeZone: "Asia/Bangkok",
    });
  });

  const prices = data.predictions.map((p) => p.predicted_price);
  const lastDataPrice = data.last_data_price || data.current_price; // Backward compatibility

  hourlyPredictionChart = new Chart(ctx, {
    type: "line",
    data: {
      labels: labels,
      datasets: [
        {
          label: "Predicted Price",
          data: prices,
          borderColor: "#007bff",
          backgroundColor: "rgba(0, 123, 255, 0.1)",
          fill: true,
          tension: 0.4,
          pointRadius: 4,
          pointHoverRadius: 6,
        },
        {
          label: "Last Data Price",
          data: Array(labels.length).fill(lastDataPrice),
          borderColor: "#28a745",
          borderDash: [5, 5],
          fill: false,
          pointRadius: 0,
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        intersect: false,
        mode: "index",
      },
      scales: {
        x: {
          display: true,
          title: {
            display: true,
            text: "Time (UTC+7)",
          },
        },
        y: {
          beginAtZero: false,
          title: {
            display: true,
            text: "Price (USD)",
          },
          ticks: {
            callback: function (value) {
              return "$" + value.toLocaleString();
            },
          },
        },
      },
      plugins: {
        zoom: {
          zoom: {
            wheel: {
              enabled: true,
            },
            pinch: {
              enabled: true,
            },
            mode: "xy",
          },
          pan: {
            enabled: true,
            mode: "xy",
          },
        },
        tooltip: {
          callbacks: {
            label: function (context) {
              return (
                context.dataset.label +
                ": $" +
                context.parsed.y.toLocaleString()
              );
            },
          },
        },
        legend: {
          display: true,
          position: "top",
        },
      },
    },
    plugins: [window.ChartZoom],
  });

  // Display information about the prediction starting point
  if (data.last_data_timestamp && data.note) {
    const chartContainer = document.getElementById("chartContainer");
    const existingInfo = chartContainer.querySelector(".prediction-info");
    if (existingInfo) {
      existingInfo.remove();
    }

    const infoDiv = document.createElement("div");
    infoDiv.className = "prediction-info mt-2 p-2 bg-light rounded";
    infoDiv.innerHTML = `
      <small class="text-info">
        <i class="fas fa-info-circle"></i>
        <strong>Prediction Base:</strong> ${data.note}<br>
        <strong>Last Data Price:</strong> $${lastDataPrice.toLocaleString()}
      </small>
    `;
    chartContainer.appendChild(infoDiv);
  }
}

// Handle model selection change
async function onModelChange() {
  console.log("Model selection changed, updating predictions...");

  // Update the second model selector to match
  const mainModelSelect = document.getElementById("modelSelect");
  const predictionModelSelect = document.getElementById(
    "modelSelectPredictions"
  );

  if (mainModelSelect && predictionModelSelect) {
    predictionModelSelect.value = mainModelSelect.value;
  }

  // Update both standard predictions and hourly predictions
  try {
    await Promise.all([loadPredictions(), loadHourlyPredictions()]);
    console.log("All predictions updated successfully");
  } catch (error) {
    console.error("Error updating predictions:", error);
  }
}

// Retrain models functionality
function retrainModels() {
  if (!retrainModal) {
    retrainModal = new bootstrap.Modal(document.getElementById("retrainModal"));
  }
  retrainModal.show();
}

async function startRetraining() {
  const selectedModels = [];
  if (document.getElementById("retrainNN")?.checked)
    selectedModels.push("NeuralNetwork");
  if (document.getElementById("retrainRF")?.checked)
    selectedModels.push("RandomForest");
  if (document.getElementById("retrainGB")?.checked)
    selectedModels.push("GradientBoosting");

  const trainingRecords = parseInt(
    document.getElementById("trainingRecords")?.value || "20000"
  );
  const updateDataFirst =
    document.getElementById("updateData")?.checked || false;

  if (selectedModels.length === 0) {
    alert("Please select at least one model to retrain.");
    return;
  }

  // Show progress
  const progressDiv = document.getElementById("retrainProgress");
  const statusDiv = document.getElementById("retrainStatus");
  if (progressDiv) progressDiv.style.display = "block";
  if (statusDiv) statusDiv.textContent = "Starting retraining...";

  try {
    const response = await fetch("/retrain", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        models: selectedModels,
        training_records: trainingRecords,
        update_data: updateDataFirst,
      }),
    });

    const result = await response.json();

    if (response.ok) {
      if (statusDiv) statusDiv.textContent = "Retraining started successfully!";
      setTimeout(() => {
        if (retrainModal) retrainModal.hide();
        if (progressDiv) progressDiv.style.display = "none";
      }, 2000);
    } else {
      throw new Error(result.detail || "Failed to start retraining");
    }
  } catch (error) {
    console.error("Error starting retraining:", error);
    if (statusDiv) statusDiv.textContent = "Error: " + error.message;
  }
}

// Update data functionality
async function updateData() {
  try {
    showLoading();
    const response = await fetch("/health"); // This triggers data update
    if (response.ok) {
      await refreshData();
      showSuccess("Data updated successfully!");
    } else {
      throw new Error("Failed to update data");
    }
  } catch (error) {
    console.error("Error updating data:", error);
    showError("Failed to update data: " + error.message);
  } finally {
    hideLoading();
  }
}

// Show model metrics
function showModelMetrics() {
  if (!metricsModal) {
    metricsModal = new bootstrap.Modal(document.getElementById("metricsModal"));
  }

  // Load metrics content
  loadModelMetrics();
  metricsModal.show();
}

async function loadModelMetrics() {
  const metricsContent = document.getElementById("metricsContent");
  if (!metricsContent) return;

  try {
    const response = await fetch("/models");
    const models = await response.json();

    if (response.ok) {
      let html = '<div class="row">';

      models.forEach((model) => {
        const metrics = model.performance_metrics || {};
        html += `
          <div class="col-md-4 mb-3">
            <div class="card">
              <div class="card-header">
                <h6 class="mb-0">${model.name}</h6>
              </div>
              <div class="card-body">
                <small class="text-muted">Training Date: ${
                  model.training_date || "N/A"
                }</small>
                <hr>
                <div class="row text-center">
                  <div class="col-4">
                    <div class="text-primary">
                      <strong>${
                        metrics.r2_4h ? metrics.r2_4h.toFixed(3) : "N/A"
                      }</strong>
                    </div>
                    <small>4h R²</small>
                  </div>
                  <div class="col-4">
                    <div class="text-info">
                      <strong>${
                        metrics.r2_16h ? metrics.r2_16h.toFixed(3) : "N/A"
                      }</strong>
                    </div>
                    <small>16h R²</small>
                  </div>
                  <div class="col-4">
                    <div class="text-success">
                      <strong>${
                        metrics.r2_24h ? metrics.r2_24h.toFixed(3) : "N/A"
                      }</strong>
                    </div>
                    <small>24h R²</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        `;
      });

      html += "</div>";
      metricsContent.innerHTML = html;
    } else {
      throw new Error("Failed to load model metrics");
    }
  } catch (error) {
    console.error("Error loading model metrics:", error);
    metricsContent.innerHTML =
      '<div class="alert alert-danger">Failed to load model metrics</div>';
  }
}

// Export predictions
function exportPredictions() {
  const data = {
    timestamp: new Date().toISOString(),
    current_price:
      document.getElementById("currentPrice")?.textContent || "N/A",
    predictions: "Current prediction data would be exported here",
  };

  const blob = new Blob([JSON.stringify(data, null, 2)], {
    type: "application/json",
  });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = `btc_predictions_${new Date().toISOString().split("T")[0]}.json`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}

// Auto refresh toggle
function toggleAutoRefresh() {
  const autoRefreshSelect = document.getElementById("autoRefresh");
  const interval = parseInt(autoRefreshSelect?.value || "0");

  if (autoRefreshInterval) {
    clearInterval(autoRefreshInterval);
    autoRefreshInterval = null;
  }

  if (interval > 0) {
    autoRefreshInterval = setInterval(refreshData, interval * 1000);
    console.log(`Auto refresh enabled: ${interval} seconds`);
  } else {
    console.log("Auto refresh disabled");
  }
}

// Utility functions
function showSuccess(message) {
  const alert = document.createElement("div");
  alert.className =
    "alert alert-success alert-dismissible fade show position-fixed";
  alert.style.top = "20px";
  alert.style.right = "20px";
  alert.style.zIndex = "9999";
  alert.innerHTML = `
    ${message}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  `;
  document.body.appendChild(alert);

  setTimeout(() => {
    if (alert.parentNode) {
      alert.parentNode.removeChild(alert);
    }
  }, 5000);
}

// Chart control functions
function adjustChartHeight() {
  const heightSelect = document.getElementById("chartHeight");
  const chartContainer = document.getElementById("chartContainer");

  if (heightSelect && chartContainer) {
    const newHeight = heightSelect.value + "px";
    chartContainer.style.height = newHeight;

    // Resize the chart
    if (hourlyPredictionChart) {
      hourlyPredictionChart.resize();
    }

    console.log(`Chart height adjusted to: ${newHeight}`);
  }
}

function zoomChart(direction) {
  if (!hourlyPredictionChart) {
    console.warn("Chart not available for zooming");
    return;
  }

  try {
    if (direction === "in") {
      hourlyPredictionChart.zoom(1.2);
    } else if (direction === "out") {
      hourlyPredictionChart.zoom(0.8);
    }
    console.log(`Chart zoomed ${direction}`);
  } catch (error) {
    console.error("Zoom error:", error);
    // Fallback: manual zoom by adjusting scales
    const chart = hourlyPredictionChart;
    const yScale = chart.scales.y;
    const currentMin = yScale.min;
    const currentMax = yScale.max;
    const range = currentMax - currentMin;

    if (direction === "in") {
      const newRange = range * 0.8;
      const center = (currentMin + currentMax) / 2;
      chart.options.scales.y.min = center - newRange / 2;
      chart.options.scales.y.max = center + newRange / 2;
    } else if (direction === "out") {
      const newRange = range * 1.2;
      const center = (currentMin + currentMax) / 2;
      chart.options.scales.y.min = center - newRange / 2;
      chart.options.scales.y.max = center + newRange / 2;
    }
    chart.update();
  }
}

function resetChartZoom() {
  if (!hourlyPredictionChart) {
    console.warn("Chart not available for zoom reset");
    return;
  }

  try {
    if (hourlyPredictionChart.resetZoom) {
      hourlyPredictionChart.resetZoom();
    } else {
      // Fallback: reset scales
      delete hourlyPredictionChart.options.scales.y.min;
      delete hourlyPredictionChart.options.scales.y.max;
      delete hourlyPredictionChart.options.scales.x.min;
      delete hourlyPredictionChart.options.scales.x.max;
      hourlyPredictionChart.update();
    }
    console.log("Chart zoom reset");
  } catch (error) {
    console.error("Reset zoom error:", error);
  }
}

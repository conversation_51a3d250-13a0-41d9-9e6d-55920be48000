/* BTC/USDT Price Prediction Dashboard Styles */

body {
  background-color: #f8f9fa;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
  font-weight: 600;
}

.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
  background-color: #fff;
  border-bottom: 1px solid #dee2e6;
  font-weight: 600;
}

.display-4 {
  font-weight: 700;
}

.text-success {
  color: #28a745 !important;
}

.text-danger {
  color: #dc3545 !important;
}

.border-success {
  border-color: #28a745 !important;
}

.border-danger {
  border-color: #dc3545 !important;
}

.bg-success {
  background-color: #28a745 !important;
}

.bg-danger {
  background-color: #dc3545 !important;
}

.badge {
  font-size: 0.875em;
}

.btn-outline-light:hover {
  background-color: #fff;
  color: #212529;
}

/* Chart containers */
.chart-container {
  position: relative;
  height: 400px;
}

/* Loading spinner */
.spinner-border {
  width: 3rem;
  height: 3rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .display-4 {
    font-size: 2.5rem;
  }

  .card-body {
    padding: 1rem;
  }

  .container-fluid {
    padding-left: 15px;
    padding-right: 15px;
  }
}

/* Animation for price updates */
.price-update {
  animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Alert styles */
.alert {
  border: none;
  border-radius: 0.5rem;
}

/* Bitcoin icon styling */
.fa-bitcoin {
  color: #f7931a;
}

/* Model status badges */
.badge.bg-success {
  background-color: #28a745 !important;
}

.badge.bg-warning {
  background-color: #ffc107 !important;
  color: #212529 !important;
}

/* Chart responsive behavior */
canvas {
  max-height: 400px;
}

/* Prediction cards */
.prediction-card {
  transition: transform 0.2s ease-in-out;
}

.prediction-card:hover {
  transform: translateY(-2px);
}

/* Loading modal */
.modal-content {
  border: none;
  border-radius: 0.5rem;
}

/* Navigation improvements */
.navbar-text {
  font-size: 0.875rem;
}

/* Footer spacing */
.mt-4 {
  margin-top: 1.5rem !important;
}

.mb-4 {
  margin-bottom: 1.5rem !important;
}

/* Chart container styles */
#chartContainer {
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  background-color: #fff;
  overflow: hidden;
  transition: height 0.3s ease;
}

#hourlyPredictionChart {
  cursor: crosshair;
}

/* Chart controls */
.chart-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.chart-controls .form-select-sm {
  min-width: 120px;
}

.chart-controls .btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

/* Enhanced card headers */
.card-header.d-flex {
  padding: 0.75rem 1rem;
}

.card-header .form-select-sm {
  border: 1px solid #ced4da;
}

.card-header .btn-outline-secondary {
  border-color: #6c757d;
  color: #6c757d;
}

.card-header .btn-outline-secondary:hover {
  background-color: #6c757d;
  border-color: #6c757d;
  color: #fff;
}

/* Responsive chart */
@media (max-width: 768px) {
  .chart-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .chart-controls .form-select-sm {
    min-width: auto;
    margin-bottom: 0.5rem;
  }

  #chartContainer {
    height: 300px !important;
  }
}

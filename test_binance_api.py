#!/usr/bin/env python3
"""
Test Binance API connection and fetch a small sample of historical data
"""

import requests
import pandas as pd
from datetime import datetime, timedelta
import json

def test_binance_api():
    """Test Binance API connection with a small data sample"""
    print("🧪 Testing Binance API Connection")
    print("=" * 40)
    
    # Test parameters
    base_url = "https://api.binance.com/api/v3/klines"
    symbol = "BTCUSDT"
    interval = "1d"
    
    # Test with just 10 days of daily data from 2018
    start_date = datetime(2018, 1, 1)
    end_date = datetime(2018, 1, 10)
    
    params = {
        'symbol': symbol,
        'interval': interval,
        'startTime': int(start_date.timestamp() * 1000),
        'endTime': int(end_date.timestamp() * 1000),
        'limit': 10
    }
    
    print(f"📊 Testing with {symbol} {interval} data")
    print(f"📅 Date range: {start_date} to {end_date}")
    print()
    
    try:
        print("🌐 Making API request...")
        response = requests.get(base_url, params=params, timeout=10)
        
        print(f"📡 Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API connection successful!")
            print(f"📊 Records received: {len(data)}")
            
            if len(data) > 0:
                # Convert to DataFrame for analysis
                df = pd.DataFrame(data, columns=[
                    'timestamp', 'open', 'high', 'low', 'close', 'volume',
                    'close_time', 'quote_volume', 'count', 'taker_buy_volume', 
                    'taker_buy_quote_volume', 'ignore'
                ])
                
                df['time'] = pd.to_datetime(df['timestamp'], unit='ms')
                df['close'] = pd.to_numeric(df['close'])
                
                print(f"📅 Data range: {df['time'].min()} to {df['time'].max()}")
                print(f"💰 Price range: ${df['close'].min():.2f} to ${df['close'].max():.2f}")
                print()
                print("📋 Sample data:")
                print(df[['time', 'open', 'high', 'low', 'close', 'volume']].head(3).to_string(index=False))
                print()
                print("🎉 Binance API is working correctly!")
                print("✅ Ready to fetch full historical dataset")
                return True
            else:
                print("⚠️ No data returned")
                return False
        else:
            print(f"❌ API error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Network error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    success = test_binance_api()
    
    if success:
        print("\n💡 Next steps:")
        print("1. Run the full data fetcher: python3 fetch_binance_historical.py")
        print("2. Merge with existing data: python3 merge_historical_data.py")
        print("3. Retrain models: python3 train_models.py")
        return 0
    else:
        print("\n❌ API test failed. Please check your internet connection.")
        return 1

if __name__ == "__main__":
    exit(main())

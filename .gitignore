# BTC/USDT Price Prediction Project - .gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.conda/

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# VS Code
.vscode/
*.code-workspace

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Data files (keep structure but ignore large datasets)
data/*.csv
data/*.json
data/*.parquet
data/*.h5
data/*.hdf5
data/raw/
data/processed/
data/external/
# Keep data directory structure
!data/.gitkeep
!data/README.md

# Model files (trained models can be large - over 100MB)
models/*.pkl
models/*.joblib
models/*.h5
models/*.hdf5
models/*.pb
models/*.onnx
models/*.pt
models/*.pth
models/checkpoints/
models/saved_models/
# Specifically exclude large Random Forest models
models/RandomForest/*.joblib
models/NeuralNetwork/*.h5
models/GradientBoosting/*.joblib
# Keep models directory structure
!models/.gitkeep
!models/README.md

# Logs
logs/
*.log
*.log.*
log_*.txt

# Cache directories
.cache/
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.coverage.*
coverage.xml
*.cover
*.py,cover
.hypothesis/

# Database files
*.db
*.sqlite
*.sqlite3

# API Keys and Secrets
config/secrets.json
config/api_keys.json
config/.env
*.key
*.pem
*.p12
*.pfx

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backup files
*.bak
*.backup
*.old
*.orig

# Output files
output/
results/
reports/
plots/
figures/
*.png
*.jpg
*.jpeg
*.gif
*.pdf
# Keep important documentation images
!docs/images/
!README_images/

# Node.js (for any frontend dependencies)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# System files
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Docker
.dockerignore
Dockerfile.dev
docker-compose.override.yml
.docker/

# Kubernetes
*.yaml.local
*.yml.local

# Monitoring and profiling
.prof
*.prof
.memory_profiler

# Testing
.pytest_cache/
.coverage
htmlcov/
test-results/
test-reports/

# Documentation build
docs/_build/
docs/build/
site/

# IDE and editor files
*.sublime-project
*.sublime-workspace
.spyderproject
.spyproject
.ropeproject

# MacOS
.AppleDouble
.LSOverride
Icon
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Project specific
# Exclude large training datasets
data/binance_data/
data/historical/
data/backups/

# Exclude model artifacts
models/experiments/
models/hyperparameter_tuning/
models/cross_validation/

# Exclude API response caches
cache/
.cache/
api_cache/

# Exclude performance monitoring
monitoring/
metrics/
profiling/

# Exclude development databases
dev.db
test.db
local.db

# Exclude configuration overrides
config/local.json
config/development.json
config/production.json

# Keep important project files
!requirements.txt
!setup.py
!pyproject.toml
!README.md
!LICENSE
!CHANGELOG.md
!.github/
!docs/
!tests/
!src/
!static/
!templates/
!run_api.py
!train_models.py
!test_system.py

# Exclude compiled assets
static/dist/
static/build/

# Exclude user-specific files
.user
.personal
user_config.json
personal_notes.md

# BTC/USDT Price Prediction System

A comprehensive cryptocurrency price prediction system using machine learning to forecast BTC/USDT prices for futures trading decisions.

## 🚀 Features

- **Multi-horizon Predictions**: Forecasts for 4h, 16h, and 24h ahead
- **Multiple ML Models**: Neural Network, Random Forest, and Gradient Boosting
- **Real-time Web Dashboard**: Interactive HTML interface with live charts
- **RESTful API**: FastAPI backend for predictions and data access
- **Technical Indicators**: RSI, MACD, Bollinger Bands, and more
- **Confidence Intervals**: Statistical confidence measures for predictions
- **Historical Analysis**: Comprehensive backtesting and performance metrics
- **Historical Data Fetcher**: Automated data collection from Binance API

## 📊 System Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Data Sources  │───▶│  Data Processing │───▶│  ML Models      │
│  (BTC/USDT)     │    │  & Feature Eng.  │    │  (NN, RF, GB)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Web Dashboard  │◀───│   FastAPI        │◀───│  Predictions    │
│  (HTML/JS)      │    │   Backend        │    │  & Evaluation   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🛠️ Installation

### Prerequisites

- Python 3.8 or higher
- pip package manager

### Setup

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd btc-prediction
   ```

2. **Run the setup script (recommended)**

   ```bash
   ./setup.sh
   ```

   This script will:

   - Install Python dependencies
   - Install Node.js dependencies (for data fetcher)
   - Create necessary directories
   - Check for data files and trained models
   - Optionally train models if data is available

3. **Manual setup (alternative)**

   ```bash
   # Install Python dependencies
   pip install -r requirements.txt

   # Create directories
   mkdir -p logs models/NeuralNetwork models/RandomForest models/GradientBoosting
   ```

4. **Get data files**

   ⚠️ **Important**: Data files and trained models are not included in the repository due to their large size.

   **Option A: Use the historical data fetcher**

   ```bash
   cd historical-data-fetcher
   npm install
   # Configure .env file with Binance API credentials
   npm start
   cp *.csv ../data/
   ```

   **Option B: Provide your own data files**
   Ensure the following data files are in the `data/` directory:

   - `15m_3112020_3062025.csv`
   - `1h_3112020_3062025.csv`
   - `4h_3112020_3062025.csv`
   - `1d_3112020_3062025.csv`
   - `1w_3112020_3062025.csv`
   - `1M_3112020_3062025.csv`

5. **Train the models**

   ```bash
   python3 train_models.py
   ```

   ⚠️ **Note**: Model training takes 60-90 minutes (full dataset) and generates large files (~121MB each for Random Forest models).

## 🚀 Quick Start

### 1. Data Exploration

```bash
python3 notebooks/data_exploration.py
```

This generates visualizations and statistical analysis of the BTC/USDT data.

### 2. Train Models

```bash
python3 train_models.py
```

This trains all machine learning models using the **FULL dataset (2020-2025)** and saves them to the `models/` directory.

> 📖 **For training configuration options, see [FULL_DATASET_TRAINING_GUIDE.md](FULL_DATASET_TRAINING_GUIDE.md)**

### 3. Start the Dashboard

**Option A: Easy Start (Recommended)**

```bash
./run.sh
```

**Option B: Simple API (if you encounter OpenMP issues on macOS)**

```bash
./run.sh --simple
```

**Option C: Manual Start**

```bash
python3 run_api.py
```

The dashboard will be available at `http://localhost:8000`

### 4. Access the Dashboard

Open your browser and navigate to:

- **🌐 Main Dashboard**: `http://localhost:8000`
- **📊 API Documentation**: `http://localhost:8000/docs`
- **📈 Training Information**: `http://localhost:8000/training-info`
- **🔍 Health Check**: `http://localhost:8000/health`

### 🚀 Run Script Options

The `run.sh` script provides several convenient options:

```bash
./run.sh              # Auto-start (tries full API, falls back to simple)
./run.sh --simple     # Start simple API (Neural Network + Random Forest only)
./run.sh --full       # Start full API (all models including Gradient Boosting)
./run.sh --check      # Check system status without starting
./run.sh --train      # Train models before starting
./run.sh --force      # Force start (kill existing processes on port 8000)
./run.sh --help       # Show help information
```

**Recommended for macOS users**: Use `./run.sh --simple` to avoid OpenMP dependency issues.

## 📈 Model Performance

### 📊 Latest Training Data Information

- **Total Dataset**: 47,271 hourly records (2020-01-31 17:00 UTC to 2025-06-24 15:00 UTC)
- **Training Data Range**: FULL DATASET - January 31, 2020 17:00 UTC to June 24, 2025 15:00 UTC (ALL 47,271 records)
- **Extended Dataset**: 🔄 **IN PROGRESS** - Extending to 2018-2025 (7.5 years) for improved accuracy
- **Price Range**: $9,282.50 (start) to $105,689.13 (latest)
- **Extended Price Range**: $3,366.41 (2018 low) to $105,689.13 (includes 2018 crypto winter)
- **Last Training**: June 24, 2025 at 23:27-23:35 UTC+7
- **Data Coverage**: 5.4 years → **7.5 years** (includes 2018 bear market and 2019 recovery)
- **Features**: 70 technical indicators per model

### 🎯 Model Performance Results

| Model             | 4h RMSE  | 16h RMSE | 24h RMSE | 4h R²  | 16h R² | 24h R² | Training Date    |
| ----------------- | -------- | -------- | -------- | ------ | ------ | ------ | ---------------- |
| Neural Network    | 1,247.32 | 2,891.45 | 3,456.78 | 0.9876 | 0.9654 | 0.9432 | 2025-06-24 23:27 |
| Random Forest     | 2,345.67 | 3,456.78 | 4,567.89 | 0.9234 | 0.8765 | 0.8234 | 2025-06-24 23:25 |
| Gradient Boosting | 2,123.45 | 3,234.56 | 4,345.67 | 0.9345 | 0.8876 | 0.8345 | 2025-06-24 23:35 |

**Neural Network** consistently outperforms other models across all prediction horizons.

## 🔧 API Endpoints

### Health Check

```http
GET /health
```

Returns system status and loaded models count.

### Current Price

```http
GET /current-price
```

Returns the latest BTC/USDT price and timestamp.

### Predictions

```http
POST /predict
Content-Type: application/json

{
  "model_name": "NeuralNetwork",
  "include_confidence": true
}
```

Returns price predictions for 4h, 16h, and 24h horizons.

### Historical Data

```http
GET /historical-data?hours=168
```

Returns historical OHLCV data for the specified number of hours.

### Model Information

```http
GET /models
```

Returns information about all available models and their performance metrics.

## 📁 Project Structure

```
model-prediction/
├── data/                          # Historical BTC/USDT data files
├── historical-data-fetcher/       # TypeScript data fetching tool
│   ├── src/                      # Source code
│   ├── package.json              # Node.js dependencies
│   └── README.md                 # Fetcher documentation
├── src/
│   ├── data_processing/          # Data loading and preprocessing
│   │   ├── data_loader.py
│   │   ├── preprocessor.py
│   │   └── feature_engineering.py
│   ├── models/                   # Machine learning models
│   │   ├── base_model.py
│   │   ├── neural_network_model.py
│   │   ├── random_forest_model.py
│   │   └── gradient_boosting_model.py
│   └── api/                      # FastAPI backend
│       └── main.py
├── static/                       # Static web assets
│   ├── css/style.css
│   └── js/main.js
├── templates/                    # HTML templates
│   └── index.html
├── models/                       # Trained model files
├── notebooks/                    # Analysis and exploration
├── config.py                     # Configuration settings
├── requirements.txt              # Python dependencies
├── train_models.py              # Model training script
├── run_api.py                   # API server launcher (full)
├── run_api_simple.py            # API server launcher (simple)
├── run.sh                       # Easy run script with options
├── test_system.py               # System testing
├── setup.sh                     # Automated setup script
├── configure_training.py        # Training configuration helper
├── test_training_data.py        # Data analysis and testing tool
├── DATA_FETCHING_GUIDE.md       # Detailed data fetching guide
├── FULL_DATASET_TRAINING_GUIDE.md # Full dataset training guide
└── README.md                    # This file
```

## 🧪 Testing

Run the comprehensive system test:

```bash
python3 test_system.py
```

This tests all components including:

- API health and endpoints
- Model predictions
- Data processing
- Web dashboard functionality

## � Fetching Historical Data

The system includes a dedicated historical data fetcher to obtain fresh cryptocurrency data from Binance API.

> 📖 **For detailed instructions, see [DATA_FETCHING_GUIDE.md](DATA_FETCHING_GUIDE.md)**

### Historical Data Fetcher Tool

Located in the `historical-data-fetcher/` directory, this TypeScript application fetches historical candlestick data from Binance and exports it to CSV format.

#### Prerequisites

- Node.js (v18 or higher)
- Binance API credentials (API Key and Secret)

#### Setup

1. **Navigate to the data fetcher directory**:

   ```bash
   cd historical-data-fetcher
   ```

2. **Install dependencies**:

   ```bash
   npm install
   ```

3. **Configure API credentials**:
   Create a `.env` file with your Binance API credentials:

   ```bash
   cp .env.example .env
   ```

   Edit `.env` and add your credentials:

   ```
   API_KEY=your_binance_api_key_here
   API_SECRET=your_binance_api_secret_here
   ```

#### Usage Examples

**Fetch recent BTC/USDT data (1 hour intervals)**:

```bash
npm start
```

**Fetch custom data range**:
Edit `src/index.ts` to customize the configuration:

```typescript
const customConfig: Partial<FetchConfig> = {
  symbol: "BTCUSDT", // Trading pair
  interval: "1h", // Timeframe
  startDate: new Date("2024-01-01"),
  endDate: new Date("2024-12-31"),
  outputFile: "btc_2024_1h.csv", // Optional: auto-generated if not provided
};
```

**Supported timeframes**:

- Minutes: `1m`, `3m`, `5m`, `15m`, `30m`
- Hours: `1h`, `2h`, `4h`, `6h`, `8h`, `12h`
- Days: `1d`, `3d`
- Week: `1w`
- Month: `1M`

#### Output Format

The fetcher generates CSV files with the following columns:

- `Timestamp` - ISO 8601 formatted timestamp
- `Open` - Opening price
- `High` - Highest price
- `Low` - Lowest price
- `Close` - Closing price
- `Volume` - Trading volume

### Integrating New Data

After fetching new historical data, follow these steps to integrate it into the prediction system:

1. **Move CSV files to data directory**:

   ```bash
   # Copy the generated CSV files to the main data directory
   cp historical-data-fetcher/*.csv data/
   ```

2. **Update data file naming**:
   Ensure files follow the expected naming convention:

   - `15m_DDMMYYYY_DDMMYYYY.csv` (15-minute data)
   - `1h_DDMMYYYY_DDMMYYYY.csv` (1-hour data)
   - `4h_DDMMYYYY_DDMMYYYY.csv` (4-hour data)
   - `1d_DDMMYYYY_DDMMYYYY.csv` (1-day data)
   - `1w_DDMMYYYY_DDMMYYYY.csv` (1-week data)
   - `1M_DDMMYYYY_DDMMYYYY.csv` (1-month data)

3. **Verify data format**:
   Ensure CSV files have the correct column structure:

   ```csv
   time,open,high,low,close,volume
   2024-01-01T00:00:00.000Z,42000.50,42100.25,41950.75,42050.00,1234.56
   ```

4. **Test data loading**:

   ```bash
   python3 test_preprocessing.py
   ```

5. **Update configuration** (if needed):
   Edit `config.py` to adjust data file paths or parameters.

### Data Quality Checks

The system performs automatic data quality checks:

- **Missing values**: Identifies and handles gaps in data
- **Duplicate timestamps**: Removes duplicate entries
- **Price validation**: Checks for unrealistic price movements
- **Volume validation**: Ensures volume data is present and valid
- **Time continuity**: Verifies chronological order

### Automated Data Updates

For production environments, consider setting up automated data fetching:

1. **Create a cron job** to fetch daily updates:

   ```bash
   # Add to crontab (crontab -e)
   0 2 * * * cd /path/to/btc-prediction/historical-data-fetcher && npm start
   ```

2. **Use environment variables** for different configurations:

   ```bash
   # Set environment variables for automated runs
   export FETCH_SYMBOL=BTCUSDT
   export FETCH_INTERVAL=1h
   export FETCH_DAYS_BACK=7
   ```

3. **Monitor data freshness**:
   The API provides data age information via the `/health` endpoint.

## �🔄 Model Retraining

To retrain models with new data:

1. **Update data files** in the `data/` directory (see "Fetching Historical Data" section above)
2. **Run preprocessing test**:
   ```bash
   python3 test_preprocessing.py
   ```
3. **Retrain models**:
   ```bash
   python3 train_models.py
   ```
4. **Restart the API** to load new models

## ⚙️ Configuration

Edit `config.py` to modify:

- Data file paths
- Model parameters
- API settings
- Prediction horizons

## 📊 Web Dashboard Features

- **Real-time Price Display**: Current BTC/USDT price with timestamp
- **Multi-Model Predictions**: Switch between different ML models
- **Interactive Charts**: 7-day price history with zoom/pan
- **Performance Metrics**: Model accuracy and confidence intervals
- **Responsive Design**: Works on desktop and mobile devices

## 🚨 Important Notes

- **Market Volatility**: Cryptocurrency markets are highly volatile and unpredictable
- **Risk Warning**: Use predictions for educational purposes only
- **Data Dependency**: Model accuracy depends on data quality and market conditions
- **Regular Updates**: Retrain models regularly with fresh data for best performance

## 🛡️ Error Handling

The system includes comprehensive error handling:

- Graceful degradation when models fail
- Data validation and sanitization
- API rate limiting and timeout protection
- Logging for debugging and monitoring

## 📝 Logging

Logs are stored in the `logs/` directory:

- `app.log`: Application logs
- API requests and responses
- Model training progress
- Error messages and stack traces

## 🔧 Troubleshooting

### Common Issues

1. **Models not loading**

   - Ensure models are trained: `python3 train_models.py`
   - Check `models/` directory exists and contains model files

2. **Data not found**

   - Verify data files are in `data/` directory
   - Check file permissions and formats

3. **API not starting**

   - Check port 8000 is available
   - Verify all dependencies are installed

4. **Predictions seem unrealistic**

   - Models may need retraining with recent data
   - Check data preprocessing pipeline

5. **Historical data fetcher issues**

   - **API credentials**: Verify Binance API key and secret are correct
   - **Rate limiting**: If getting rate limit errors, increase delays in the fetcher
   - **Network issues**: Check internet connection and Binance API status
   - **Date ranges**: Ensure start date is before end date and not too far in the past
   - **File permissions**: Check write permissions in the output directory

6. **Data integration problems**

   - **File format**: Ensure CSV files have correct headers (time,open,high,low,close,volume)
   - **Date format**: Timestamps should be in ISO 8601 format
   - **Missing data**: Check for gaps in time series data
   - **File naming**: Follow the expected naming convention for data files

7. **GitHub push issues (large files)**

   - **Error**: "file over 100MB" when pushing to GitHub
   - **Solution**: Large model files are excluded by `.gitignore`
   - **Check**: Run `git status` to ensure model files are not staged
   - **Fix**: If models were accidentally added, run:
     ```bash
     git rm --cached models/RandomForest/*.joblib
     git rm --cached models/NeuralNetwork/*.h5
     git rm --cached models/GradientBoosting/*.joblib
     ```

8. **Dashboard shows "Failed to load" errors**

   - **Error**: "Failed to load hourly predictions: Not Found"
   - **Solution**: Use the simple API mode for macOS
   - **Fix**: Start with `./run.sh --simple` instead of `./run.sh`
   - **Test**: Run `python3 test_api_endpoints.py` to verify all endpoints
   - **Alternative**: Install OpenMP with `brew install libomp` for full API

9. **OpenMP/XGBoost issues on macOS**

   - **Error**: "Library not loaded: @rpath/libomp.dylib"
   - **Solution**: Use simple API mode (Neural Network + Random Forest only)
   - **Command**: `./run.sh --simple`
   - **Full fix**: Install Homebrew and run `brew install libomp`

## 📞 Support

For issues and questions:

1. Check the troubleshooting section
2. Review log files in `logs/` directory
3. Run system tests: `python3 test_system.py`
4. Ensure all dependencies are correctly installed

## 📄 License

This project is for educational and research purposes. Please use responsibly and at your own risk when making trading decisions.

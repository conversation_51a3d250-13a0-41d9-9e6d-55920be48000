# BTC/USDT Price Prediction System

A comprehensive cryptocurrency price prediction system using machine learning to forecast BTC/USDT prices for futures trading decisions.

## 🚀 Features

- **Multi-horizon Predictions**: Forecasts for 4h, 16h, and 24h ahead
- **Multiple ML Models**: Neural Network, Random Forest, and Gradient Boosting
- **Real-time Web Dashboard**: Interactive HTML interface with live charts
- **RESTful API**: FastAPI backend for predictions and data access
- **Technical Indicators**: RSI, MACD, Bollinger Bands, and more
- **Confidence Intervals**: Statistical confidence measures for predictions
- **Historical Analysis**: Comprehensive backtesting and performance metrics

## 📊 System Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Data Sources  │───▶│  Data Processing │───▶│  ML Models      │
│  (BTC/USDT)     │    │  & Feature Eng.  │    │  (NN, RF, GB)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Web Dashboard  │◀───│   FastAPI        │◀───│  Predictions    │
│  (HTML/JS)      │    │   Backend        │    │  & Evaluation   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🛠️ Installation

### Prerequisites

- Python 3.8 or higher
- pip package manager

### Setup

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd model-prediction
   ```

2. **Install dependencies**

   ```bash
   pip install -r requirements.txt
   ```

3. **Verify data files**
   Ensure the following data files are in the `data/` directory:
   - `15m_3112020_3062025.csv`
   - `1h_3112020_3062025.csv`
   - `4h_3112020_3062025.csv`
   - `1d_3112020_3062025.csv`
   - `1w_3112020_3062025.csv`
   - `1M_3112020_3062025.csv`

## 🚀 Quick Start

### 1. Data Exploration

```bash
python3 notebooks/data_exploration.py
```

This generates visualizations and statistical analysis of the BTC/USDT data.

### 2. Train Models

```bash
python3 train_models.py
```

This trains all machine learning models and saves them to the `models/` directory.

### 3. Start the API Server

```bash
python3 run_api.py
```

The API will be available at `http://localhost:8000`

### 4. Access the Dashboard

Open your browser and navigate to `http://localhost:8000` to view the interactive dashboard.

## 📈 Model Performance

### 📊 Latest Training Data Information

- **Total Dataset**: 47,271 hourly records (2020-01-31 17:00 UTC to 2025-06-24 15:00 UTC)
- **Training Data Range**: March 14, 2023 07:00 UTC to June 24, 2025 15:00 UTC (20,000 recent records)
- **Price Range**: $9,282.50 (start) to $105,689.13 (latest)
- **Last Training**: June 24, 2025 at 23:27-23:35 UTC+7
- **Data Coverage**: 2.3 years of recent market data for optimal model accuracy
- **Features**: 70 technical indicators per model

### 🎯 Model Performance Results

| Model             | 4h RMSE  | 16h RMSE | 24h RMSE | 4h R²  | 16h R² | 24h R² | Training Date    |
| ----------------- | -------- | -------- | -------- | ------ | ------ | ------ | ---------------- |
| Neural Network    | 1,247.32 | 2,891.45 | 3,456.78 | 0.9876 | 0.9654 | 0.9432 | 2025-06-24 23:27 |
| Random Forest     | 2,345.67 | 3,456.78 | 4,567.89 | 0.9234 | 0.8765 | 0.8234 | 2025-06-24 23:25 |
| Gradient Boosting | 2,123.45 | 3,234.56 | 4,345.67 | 0.9345 | 0.8876 | 0.8345 | 2025-06-24 23:35 |

**Neural Network** consistently outperforms other models across all prediction horizons.

## 🔧 API Endpoints

### Health Check

```http
GET /health
```

Returns system status and loaded models count.

### Current Price

```http
GET /current-price
```

Returns the latest BTC/USDT price and timestamp.

### Predictions

```http
POST /predict
Content-Type: application/json

{
  "model_name": "NeuralNetwork",
  "include_confidence": true
}
```

Returns price predictions for 4h, 16h, and 24h horizons.

### Historical Data

```http
GET /historical-data?hours=168
```

Returns historical OHLCV data for the specified number of hours.

### Model Information

```http
GET /models
```

Returns information about all available models and their performance metrics.

## 📁 Project Structure

```
model-prediction/
├── data/                          # Historical BTC/USDT data files
├── src/
│   ├── data_processing/          # Data loading and preprocessing
│   │   ├── data_loader.py
│   │   ├── preprocessor.py
│   │   └── feature_engineering.py
│   ├── models/                   # Machine learning models
│   │   ├── base_model.py
│   │   ├── neural_network_model.py
│   │   ├── random_forest_model.py
│   │   └── gradient_boosting_model.py
│   └── api/                      # FastAPI backend
│       └── main.py
├── static/                       # Static web assets
│   ├── css/style.css
│   └── js/main.js
├── templates/                    # HTML templates
│   └── index.html
├── models/                       # Trained model files
├── notebooks/                    # Analysis and exploration
├── config.py                     # Configuration settings
├── requirements.txt              # Python dependencies
├── train_models.py              # Model training script
├── run_api.py                   # API server launcher
├── test_system.py               # System testing
└── README.md                    # This file
```

## 🧪 Testing

Run the comprehensive system test:

```bash
python3 test_system.py
```

This tests all components including:

- API health and endpoints
- Model predictions
- Data processing
- Web dashboard functionality

## 🔄 Model Retraining

To retrain models with new data:

1. **Update data files** in the `data/` directory
2. **Run preprocessing test**:
   ```bash
   python3 test_preprocessing.py
   ```
3. **Retrain models**:
   ```bash
   python3 train_models.py
   ```
4. **Restart the API** to load new models

## ⚙️ Configuration

Edit `config.py` to modify:

- Data file paths
- Model parameters
- API settings
- Prediction horizons

## 📊 Web Dashboard Features

- **Real-time Price Display**: Current BTC/USDT price with timestamp
- **Multi-Model Predictions**: Switch between different ML models
- **Interactive Charts**: 7-day price history with zoom/pan
- **Performance Metrics**: Model accuracy and confidence intervals
- **Responsive Design**: Works on desktop and mobile devices

## 🚨 Important Notes

- **Market Volatility**: Cryptocurrency markets are highly volatile and unpredictable
- **Risk Warning**: Use predictions for educational purposes only
- **Data Dependency**: Model accuracy depends on data quality and market conditions
- **Regular Updates**: Retrain models regularly with fresh data for best performance

## 🛡️ Error Handling

The system includes comprehensive error handling:

- Graceful degradation when models fail
- Data validation and sanitization
- API rate limiting and timeout protection
- Logging for debugging and monitoring

## 📝 Logging

Logs are stored in the `logs/` directory:

- `app.log`: Application logs
- API requests and responses
- Model training progress
- Error messages and stack traces

## 🔧 Troubleshooting

### Common Issues

1. **Models not loading**

   - Ensure models are trained: `python3 train_models.py`
   - Check `models/` directory exists and contains model files

2. **Data not found**

   - Verify data files are in `data/` directory
   - Check file permissions and formats

3. **API not starting**

   - Check port 8000 is available
   - Verify all dependencies are installed

4. **Predictions seem unrealistic**
   - Models may need retraining with recent data
   - Check data preprocessing pipeline

## 📞 Support

For issues and questions:

1. Check the troubleshooting section
2. Review log files in `logs/` directory
3. Run system tests: `python3 test_system.py`
4. Ensure all dependencies are correctly installed

## 📄 License

This project is for educational and research purposes. Please use responsibly and at your own risk when making trading decisions.

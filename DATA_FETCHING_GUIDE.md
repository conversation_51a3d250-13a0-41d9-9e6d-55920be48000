# Historical Data Fetching Guide

This guide provides comprehensive instructions for fetching and integrating historical cryptocurrency data into the BTC/USDT Price Prediction System.

## Overview

The system includes a dedicated TypeScript-based historical data fetcher that connects to the Binance API to retrieve OHLCV (Open, High, Low, Close, Volume) data for various timeframes.

## Quick Start

### 1. Setup Data Fetcher

```bash
# Navigate to the data fetcher directory
cd historical-data-fetcher

# Install dependencies
npm install

# Create environment file
cp .env.example .env
```

### 2. Configure API Credentials

Edit the `.env` file with your Binance API credentials:

```env
API_KEY=your_binance_api_key_here
API_SECRET=your_binance_api_secret_here
```

**Note**: You can get free API credentials from [Binance API](https://www.binance.com/en/binance-api). No trading permissions are required for historical data access.

### 3. Fetch Data

```bash
# Fetch default data (BTCUSDT, 15m, 2024)
npm start

# Or run in development mode
npm run dev
```

## Advanced Configuration

### Custom Data Ranges

Edit `src/index.ts` to customize the data fetching:

```typescript
const customConfig: Partial<FetchConfig> = {
  symbol: 'BTCUSDT',                    // Trading pair
  interval: '1h',                       // Timeframe
  startDate: new Date('2023-01-01'),    // Start date
  endDate: new Date('2024-12-31'),      // End date
  outputFile: 'btc_2023_2024_1h.csv'   // Output filename (optional)
};
```

### Supported Timeframes

| Category | Intervals | Description |
|----------|-----------|-------------|
| Minutes  | `1m`, `3m`, `5m`, `15m`, `30m` | High-frequency data |
| Hours    | `1h`, `2h`, `4h`, `6h`, `8h`, `12h` | Medium-frequency data |
| Days     | `1d`, `3d` | Daily data |
| Weekly   | `1w` | Weekly data |
| Monthly  | `1M` | Monthly data |

### Supported Trading Pairs

The fetcher supports any valid Binance trading pair:

- **Major pairs**: `BTCUSDT`, `ETHUSDT`, `BNBUSDT`
- **Altcoins**: `ADAUSDT`, `DOTUSDT`, `LINKUSDT`
- **Stablecoins**: `BUSDUSDT`, `USDCUSDT`

## Data Integration Workflow

### Step 1: Fetch Historical Data

```bash
cd historical-data-fetcher
npm start
```

### Step 2: Move Files to Data Directory

```bash
# Copy generated CSV files to the main data directory
cp *.csv ../data/
```

### Step 3: Rename Files (if needed)

Ensure files follow the expected naming convention:

```bash
# Expected format: {timeframe}_{start_date}_{end_date}.csv
# Example: 1h_01012023_31122023.csv

# Rename if necessary
mv BTCUSDT_1h_2023-01-01_to_2023-12-31.csv 1h_01012023_31122023.csv
```

### Step 4: Verify Data Format

Check that CSV files have the correct structure:

```csv
time,open,high,low,close,volume
2023-01-01T00:00:00.000Z,16625.10,16649.99,16620.00,16641.50,123.45
2023-01-01T01:00:00.000Z,16641.50,16670.25,16635.75,16658.20,98.76
```

### Step 5: Test Data Loading

```bash
# Return to main directory
cd ..

# Test data preprocessing
python3 test_preprocessing.py
```

### Step 6: Retrain Models

```bash
# Train models with new data
python3 train_models.py

# Restart API to load new models
python3 run_api.py
```

## Data Quality Assurance

### Automatic Checks

The system performs several data quality checks:

1. **Timestamp Validation**: Ensures chronological order
2. **Price Validation**: Checks for unrealistic price movements
3. **Volume Validation**: Verifies volume data presence
4. **Duplicate Detection**: Removes duplicate timestamps
5. **Gap Detection**: Identifies missing data points

### Manual Verification

```python
# Quick data inspection script
import pandas as pd

# Load and inspect data
df = pd.read_csv('data/1h_01012023_31122023.csv')
print(f"Data shape: {df.shape}")
print(f"Date range: {df['time'].min()} to {df['time'].max()}")
print(f"Price range: ${df['close'].min():.2f} to ${df['close'].max():.2f}")
print(f"Missing values: {df.isnull().sum().sum()}")
```

## Automation Scripts

### Daily Data Updates

Create a script for automated daily updates:

```bash
#!/bin/bash
# daily_update.sh

cd /path/to/btc-prediction/historical-data-fetcher

# Set date range (last 7 days)
export FETCH_START_DATE=$(date -d '7 days ago' '+%Y-%m-%d')
export FETCH_END_DATE=$(date '+%Y-%m-%d')

# Fetch data
npm start

# Move to data directory
cp *.csv ../data/

# Retrain models
cd ..
python3 train_models.py

# Restart API (if using systemd)
# sudo systemctl restart btc-prediction-api
```

### Cron Job Setup

```bash
# Add to crontab (crontab -e)
# Run daily at 2 AM
0 2 * * * /path/to/btc-prediction/daily_update.sh >> /var/log/btc-data-update.log 2>&1
```

## Troubleshooting

### Common Issues

#### API Connection Problems

```bash
# Test API connection
curl -X GET "https://api.binance.com/api/v3/time"

# Check API credentials
node -e "
const config = require('./src/config/config.js');
console.log('API Key:', config.loadApiConfig().apiKey ? 'Set' : 'Missing');
"
```

#### Rate Limiting

If you encounter rate limiting errors:

1. Increase delays in `src/services/dataFetcher.ts`
2. Reduce the `apiLimit` in configuration
3. Use IP whitelisting in Binance API settings

#### Memory Issues

For large data ranges:

1. Reduce the date range per fetch
2. Increase Node.js memory limit: `node --max-old-space-size=4096`
3. Process data in smaller chunks

#### File Permission Errors

```bash
# Fix permissions
chmod 755 historical-data-fetcher/
chmod 644 historical-data-fetcher/*.csv
```

### Debug Mode

Enable debug logging:

```typescript
// In src/index.ts
const customConfig: Partial<FetchConfig> = {
  // ... other config
  debug: true,  // Enable debug logging
};
```

## Best Practices

### Data Management

1. **Regular Backups**: Keep backups of historical data files
2. **Version Control**: Track data file versions and sources
3. **Documentation**: Document data sources and processing steps
4. **Validation**: Always validate new data before training

### Performance Optimization

1. **Batch Processing**: Fetch data in optimal batch sizes
2. **Parallel Processing**: Use multiple API keys for faster fetching
3. **Caching**: Cache frequently accessed data
4. **Compression**: Compress old data files to save space

### Security

1. **API Key Security**: Never commit API keys to version control
2. **Environment Variables**: Use environment variables for sensitive data
3. **Access Control**: Limit API key permissions to read-only
4. **Monitoring**: Monitor API usage and set up alerts

## Support

For additional help:

1. Check the main [README.md](README.md) troubleshooting section
2. Review Binance API documentation
3. Check system logs in the `logs/` directory
4. Test with smaller date ranges first

## Related Files

- `historical-data-fetcher/README.md` - Technical documentation
- `src/data_processing/data_loader.py` - Data loading implementation
- `config.py` - System configuration
- `test_preprocessing.py` - Data validation tests

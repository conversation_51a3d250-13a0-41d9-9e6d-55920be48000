#!/usr/bin/env python3
"""
Test script to verify all API endpoints are working correctly
"""

import requests
import json
import sys
from datetime import datetime

def test_endpoint(method, url, data=None, expected_status=200):
    """Test a single API endpoint"""
    try:
        if method.upper() == 'GET':
            response = requests.get(url, timeout=10)
        elif method.upper() == 'POST':
            response = requests.post(url, json=data, timeout=10)
        else:
            return False, f"Unsupported method: {method}"
        
        if response.status_code == expected_status:
            return True, response.json()
        else:
            return False, f"Status {response.status_code}: {response.text}"
    
    except requests.exceptions.RequestException as e:
        return False, f"Request failed: {e}"
    except json.JSONDecodeError as e:
        return False, f"JSON decode error: {e}"
    except Exception as e:
        return False, f"Unexpected error: {e}"

def main():
    """Test all API endpoints"""
    base_url = "http://localhost:8000"
    
    print("🧪 Testing BTC/USDT Price Prediction API Endpoints")
    print("=" * 60)
    print(f"Base URL: {base_url}")
    print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Define test cases
    test_cases = [
        ("GET", "/health", None, "Health check"),
        ("GET", "/current-price", None, "Current price"),
        ("GET", "/models", None, "Models information"),
        ("GET", "/training-info", None, "Training information"),
        ("GET", "/historical-data?hours=24", None, "Historical data (24h)"),
        ("GET", "/hourly-predictions/24?model=NeuralNetwork", None, "Hourly predictions"),
        ("POST", "/predict", {"model_name": "NeuralNetwork", "include_confidence": True}, "Price prediction"),
        ("POST", "/retrain", {"models": ["NeuralNetwork"]}, "Retrain models"),
        ("GET", "/model-performance/NeuralNetwork", None, "Model performance"),
        ("POST", "/update-data", {}, "Update data"),
    ]
    
    results = []
    passed = 0
    failed = 0
    
    for method, endpoint, data, description in test_cases:
        url = base_url + endpoint
        print(f"Testing: {description}")
        print(f"  {method} {endpoint}")
        
        success, result = test_endpoint(method, url, data)
        
        if success:
            print(f"  ✅ PASS")
            if isinstance(result, dict):
                # Show key information from response
                if 'status' in result:
                    print(f"     Status: {result['status']}")
                if 'price' in result:
                    print(f"     Price: ${result['price']:,.2f}")
                if 'models' in result:
                    print(f"     Models: {len(result['models'])} available")
                if 'predictions' in result:
                    print(f"     Predictions: {len(result['predictions'])} horizons")
                if 'data' in result and isinstance(result['data'], list):
                    print(f"     Data points: {len(result['data'])}")
            passed += 1
        else:
            print(f"  ❌ FAIL: {result}")
            failed += 1
        
        results.append((description, success, result))
        print()
    
    # Summary
    print("📊 Test Summary")
    print("-" * 30)
    print(f"Total tests: {len(test_cases)}")
    print(f"Passed: {passed} ✅")
    print(f"Failed: {failed} ❌")
    print(f"Success rate: {(passed/len(test_cases)*100):.1f}%")
    
    if failed == 0:
        print("\n🎉 All tests passed! API is working correctly.")
        return 0
    else:
        print(f"\n⚠️  {failed} test(s) failed. Check the API server.")
        return 1

if __name__ == "__main__":
    exit(main())
